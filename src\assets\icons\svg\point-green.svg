﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="23px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="620px" y="425px" width="24px" height="23px" filterUnits="userSpaceOnUse" id="filter344">
      <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.698039215686274 0  " in="shadowComposite" />
    </filter>
    <g id="widget345">
      <path d="M 631 429  C 634.92 429  638 431.86  638 435.5  C 638 439.14  634.92 442  631 442  C 627.08 442  624 439.14  624 435.5  C 624 431.86  627.08 429  631 429  Z " fill-rule="nonzero" fill="#70b603" stroke="none" />
      <path d="M 631 429.5  C 634.64 429.5  637.5 432.14  637.5 435.5  C 637.5 438.86  634.64 441.5  631 441.5  C 627.36 441.5  624.5 438.86  624.5 435.5  C 624.5 432.14  627.36 429.5  631 429.5  Z " stroke-width="1" stroke="#ffffff" fill="none" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -620 -425 )">
    <use xlink:href="#widget345" filter="url(#filter344)" />
    <use xlink:href="#widget345" />
  </g>
</svg>