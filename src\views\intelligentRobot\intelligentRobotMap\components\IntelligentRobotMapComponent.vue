<template>
  <div class="intelligent-robot-map" ref="mapContainer">
    <!-- 地图主容器 -->
    <div class="map-main-container" :class="{ 'fullscreen': isFullscreen }">
      <!-- 地图显示区域 -->
      <div class="map-display-area" ref="mapDisplayArea">
        <!-- SVG地图容器 -->
        <div
          class="svg-map-container"
          ref="svgContainer"
          @wheel="handleWheel"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseLeave"
        >
          <!-- SVG地图 -->
          <div
            class="svg-map"
            ref="svgMap"
            :style="mapTransformStyle"
            v-html="svgContent"
          ></div>

          <!-- ECharts图层 - 用于显示机器人、障碍、AI报警等 -->
          <div
            class="echarts-layer"
            ref="echartsContainer"
            :style="echartsTransformStyle"
          ></div>
        </div>

        <!-- 缩放信息显示 -->
        <div class="zoom-info" v-if="svgContent">
          <span>{{ Math.round(mapState.scale * 100) }}%</span>
        </div>

        <!-- 全屏提示 -->
        <div class="fullscreen-tip" v-if="isFullscreen">
          <span>按 ESC 退出全屏</span>
        </div>
      </div>

      <!-- 点位选择工具栏 -->
      <div class="point-toolbar" v-if="!isFullscreen">
        <div class="toolbar-content">
          <el-checkbox-group v-model="selectedPointTypes" @change="handlePointTypeChange">
            <el-checkbox
              v-for="pointType in pointTypeOptions"
              :key="pointType.value"
              :label="pointType.value"
              :disabled="!mapInfo || !mapInfo.ps || mapInfo.ps.length === 0"
            >
              <el-icon><component :is="pointType.icon" /></el-icon>
              {{ pointType.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-overlay" v-if="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>{{ loadingText }}</span>
    </div>

    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="connectionStatus">
      <el-icon>
        <component :is="connectionStatus === 'connected' ? 'Connection' : 'Disconnect'" />
      </el-icon>
      <span>{{ connectionStatusText }}</span>
    </div>
  </div>
</template>

<script setup name="IntelligentRobotMapComponent">
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import {
  Loading,
//  Connection,
//  Disconnect,
//  MapLocation,
//  Charging,
//  View,
//  TakeawayBox,
//  Position,
//  Guide,
//  Right,
//  CaretRight
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getToken } from '@/utils/auth'
import { getMapInfo } from '@/api/intelligentRobot/intelligentRobotView'
// import { calcNewCoordinateXToPt, calcNewCoordinateYToPt } from '@/utils/mapCalc' // 使用自定义坐标转换

// Props
const props = defineProps({
  // 可以接收外部传入的配置
})

// Emits
const emit = defineEmits(['map-loaded', 'websocket-connected', 'websocket-disconnected'])

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const baseWsUrl = import.meta.env.VITE_WS_URL;

// Refs
const mapContainer = ref(null)
const mapDisplayArea = ref(null)
const svgContainer = ref(null)
const svgMap = ref(null)
const echartsContainer = ref(null)

// 响应式数据
const loading = ref(false)
const loadingText = ref('正在加载地图...')
const svgContent = ref('')
const isFullscreen = ref(false)
const echartsInstance = ref(null)

// WebSocket相关
const websocket = ref(null)
const connectionStatus = ref('disconnected') // connected, connecting, disconnected
const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return 'WebSocket已连接'
    case 'connecting': return 'WebSocket连接中...'
    case 'disconnected': return 'WebSocket未连接'
    default: return 'WebSocket状态未知'
  }
})

// 地图状态
const mapState = reactive({
  scale: 1,
  translateX: 0,
  translateY: 0,
  isDragging: false,
  lastMouseX: 0,
  lastMouseY: 0
})

// 地图数据
// mapData json: {"cadUrl":"/profile/upload/???/?.svg", "scale":0.0037, "offsetX":0, "offsetY":0,
//                "minPosX":0, "maxPosX":100, "minPosY":0, "maxPosY":100,
//                "cadWidth":26.89, "cadHeight":26.89, "xmapWidth":100, "xmapHeight":100}
// cadWidth/cadHeight: CAD图纸的原始尺寸（pt为单位）
// xmapWidth/xmapHeight: 物理原始尺寸（米为单位）
// minPosX/maxPosX: xmap物理地图的左下角坐标
// maxPosX/maxPosY: xmap物理地图的右上角坐标
// scale: 统一比例尺
// offsetX/offsetY: xmap(左上角)坐标与cad图纸左上角的二次偏移量（pt为单位）
const mapData = ref(null)
const mapInfo = ref(null) // WebSocket推送的地图信息
const robotStatusList = ref([]) // 机器人状态列表

// 点位类型选项
const pointTypeOptions = ref([
  { label: '标记点', value: 'LandMark', icon: 'MapLocation' },
  { label: '充电点', value: 'ChargePoint', icon: 'Charging' },
  { label: '识别点', value: 'IdenPoint', icon: 'View' },
  { label: '红外测温', value: 'InfraredDetection', icon: 'TakeawayBox' },
  { label: '十字路口', value: 'Crossroads', icon: 'Position' },
  { label: '出口', value: 'Exit', icon: 'Right' },
  { label: '停车位', value: 'Parking', icon: 'CaretRight' }
])

const selectedPointTypes = ref(['LandMark', 'ChargePoint', 'IdenPoint']) // 默认选中的点位类型

// 计算样式
const mapTransformStyle = computed(() => {
  return {
    transform: `translate(${mapState.translateX}px, ${mapState.translateY}px) scale(${mapState.scale})`,
    transformOrigin: '0 0',
    transition: mapState.isDragging ? 'none' : 'transform 0.2s ease-out'
  }
})

const echartsTransformStyle = computed(() => {
  return {
    transform: `translate(${mapState.translateX}px, ${mapState.translateY}px) scale(${mapState.scale})`,
    transformOrigin: '0 0',
    transition: mapState.isDragging ? 'none' : 'transform 0.2s ease-out'
  }
})

// 生命周期
onMounted(() => {
  initComponent()
  setupKeyboardEvents()
})

onBeforeUnmount(() => {
  cleanup()
})

// 监听点位类型变化
watch(selectedPointTypes, () => {
  updateMapElements()
}, { deep: true })

// 监听地图信息变化
watch(mapInfo, () => {
  updateMapElements()
}, { deep: true })

// 监听机器人状态变化
watch(robotStatusList, () => {
  updateRobotElements()
}, { deep: true })

// 方法
const initComponent = async () => {
  try {
    await loadMapData()
    await initWebSocket()
    initECharts()
  } catch (error) {
    console.error('初始化组件失败:', error)
    ElMessage.error('初始化地图组件失败')
  }
}

const loadMapData = async () => {
  loading.value = true
  loadingText.value = '正在加载地图数据...'

  try {
    const query = {"mapId": -1}
    const response = await getMapInfo(query)
    if (response.code === 200 && response.data) {
      mapData.value = response.data
      console.log('地图数据:', mapData.value)

      if (response.data.cadUrl) {
        // 构建完整的SVG URL
        const fullSvgUrl = baseUrl + response.data.cadUrl

        // 加载SVG内容
        await loadSvgContent(fullSvgUrl)

        // 地图加载完成后重新初始化ECharts以使用正确的坐标系
        if (echartsInstance.value) {
          echartsInstance.value.dispose()
        }
        await nextTick()
        initECharts()

        ElMessage.success('地图加载成功')
        emit('map-loaded', mapData.value)
      } else {
        ElMessage.warning('地图数据为空')
      }
    } else {
      throw new Error(response.msg || '获取地图数据失败')
    }
  } catch (error) {
    console.error('加载地图数据失败:', error)
    ElMessage.error('加载地图数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载SVG内容
const loadSvgContent = async (svgUrl) => {
  try {
    const response = await fetch(svgUrl)
    if (response.ok) {
      const svgText = await response.text()
      svgContent.value = svgText
      console.log('SVG内容加载成功')
    } else {
      throw new Error(`加载SVG失败: ${response.status}`)
    }
  } catch (error) {
    console.error('加载SVG内容失败:', error)
    ElMessage.error('加载SVG内容失败: ' + error.message)
  }
}

const initWebSocket = async () => {
  try {
    connectionStatus.value = 'connecting'
    const token = getToken()
    const wsUrl = `${baseWsUrl}/wsDispatch?token=${encodeURIComponent(token)}`

    websocket.value = new WebSocket(wsUrl)

    websocket.value.onopen = () => {
      connectionStatus.value = 'connected'
      ElMessage.success('WebSocket连接成功')
      emit('websocket-connected')
    }

    websocket.value.onmessage = (event) => {
      handleWebSocketMessage(event)
    }

    websocket.value.onclose = () => {
      connectionStatus.value = 'disconnected'
      ElMessage.warning('WebSocket连接已断开')
      emit('websocket-disconnected')

      // 尝试重连
      setTimeout(() => {
        if (connectionStatus.value === 'disconnected') {
          initWebSocket()
        }
      }, 5000)
    }

    websocket.value.onerror = (error) => {
      console.error('WebSocket错误:', error)
      connectionStatus.value = 'disconnected'
      ElMessage.error('WebSocket连接错误')
    }
  } catch (error) {
    console.error('初始化WebSocket失败:', error)
    connectionStatus.value = 'disconnected'
  }
}

const handleWebSocketMessage = (event) => {
  try {
    const payload = JSON.parse(event.data)
    console.log('收到WebSocket消息:', payload)

    switch (payload.type) {
      case 'MAP':
        // 地图信息更新
        mapInfo.value = payload.data
        console.log('地图信息更新:', mapInfo.value)
        break

      case 'ROBOT':
        // 机器人状态更新
        robotStatusList.value = payload.data || []
        console.log('机器人状态更新:', robotStatusList.value)
        break

      case 'OBSTACLE':
        // 障碍变更
        updateObstacles(payload.data)
        break

      case 'AI_ALARM':
        // AI报警变更
        updateAiAlarms(payload.data)
        break

      default:
        console.log('未知消息类型:', payload.type)
    }
  } catch (error) {
    console.error('处理WebSocket消息失败:', error)
  }
}

const updateObstacles = (obstacles) => { // obstacles结构: [{"x":0.0,"y":0.0,"xmapId":"1","robotId":1,"type":0}]
  if (!mapInfo.value) return

  // 直接用新的障碍数据替换原有数据
  if (Array.isArray(obstacles)) {
    // 如果obstacles为空数组[]，说明所有障碍都已消除
    // 如果obstacles有数据，则更新为最新的障碍列表
    mapInfo.value.o = obstacles

    console.log('障碍数据更新:', obstacles.length > 0 ? `${obstacles.length}个障碍` : '所有障碍已清除')

    // 立即刷新地图显示
    // 不需要这句话，因为mapInfo.value变化时，会自动调用updateMapElements()
    // updateMapElements()
  }
}

const updateAiAlarms = (aiAlarms) => { // aiAlarms结构: [{"name":"漏液报警","x":1.0,"y":2.0,"type":"leak","time":"2025-01-06 10:30:00","dept":"ETCH"}]
  if (!mapInfo.value) return

  // 直接用新的AI报警数据替换原有数据
  if (Array.isArray(aiAlarms)) {
    // 如果aiAlarms为空数组[]，说明所有AI报警都已消除
    // 如果aiAlarms有数据，则更新为最新的AI报警列表
    mapInfo.value.ai = aiAlarms

    console.log('AI报警数据更新:', aiAlarms.length > 0 ? `${aiAlarms.length}个AI报警` : '所有AI报警已确认')

    // 立即刷新地图显示
    // 不需要这句话，因为mapInfo.value变化时，会自动调用updateMapElements()
    // updateMapElements()
  }
}

const initECharts = () => {
  if (!echartsContainer.value) return

  echartsInstance.value = echarts.init(echartsContainer.value)

  // 根据地图数据设置坐标系
  const getAxisRange = () => {
    if (!mapData.value) {
      return { xMin: 0, xMax: 1000, yMin: 0, yMax: 1000 }
    }

    // 根据地图的实际尺寸计算坐标范围
    // cadWidth和cadHeight是以米为单位的地图尺寸
    const { cadWidth = 100, cadHeight = 100 } = mapData.value

    // 转换为像素坐标系，假设1米 = 72pt (常用的CAD转换比例)
    const pixelScale = 72
    const xMax = cadWidth * pixelScale
    const yMax = cadHeight * pixelScale

    return {
      xMin: 0,
      xMax: xMax,
      yMin: 0,
      yMax: yMax
    }
  }

  const { xMin, xMax, yMin, yMax } = getAxisRange()

  // 设置初始配置
  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'value',
      show: false,
      min: xMin,
      max: xMax
    },
    yAxis: {
      type: 'value',
      show: false,
      min: yMin,
      max: yMax,
      inverse: true // Y轴反转，因为SVG坐标系Y轴向下
    },
    series: []
  }

  echartsInstance.value.setOption(option)

  // 监听点击事件
  echartsInstance.value.on('click', handleEChartsClick)
}

const updateMapElements = () => {
  if (!echartsInstance.value || !mapInfo.value) return

  const series = []

  // 添加障碍点 - 即使为空数组也要处理，以便清空显示
  if (mapInfo.value.o) {
    const obstacleData = mapInfo.value.o
      .filter(obstacle => obstacle.type === 0) // 只显示普通障碍
      .map(obstacle => {
        const ptX = calcCoordinateToPt(obstacle.x, false)
        const ptY = calcCoordinateToPt(obstacle.y, true)

        return {
          value: [ptX, ptY],
          name: `障碍点`,
          itemStyle: { color: '#ff4444' },
          symbolSize: 12,
          symbol: 'circle', // 使用简单的圆形图标
          obstacle: obstacle
        }
      })

    // 无论是否有数据都添加系列，空数据会清空显示
    series.push({
      name: '障碍点',
      type: 'scatter',
      data: obstacleData,
      tooltip: {
        formatter: (params) => {
          const obstacle = params.data.obstacle
          return `障碍点<br/>ID: ${obstacle.xmapId}<br/>坐标: (${obstacle.x.toFixed(2)}, ${obstacle.y.toFixed(2)})`
        }
      }
    })
  }

  // 添加AI报警点 - 即使为空数组也要处理，以便清空显示
  if (mapInfo.value.ai) {
    const aiAlarmData = mapInfo.value.ai.map(alarm => {
      const ptX = calcCoordinateToPt(alarm.x, false)
      const ptY = calcCoordinateToPt(alarm.y, true)

      const iconMap = {
        'leak': '#ff6b6b',
        'matter': '#4ecdc4',
        'temp': '#45b7d1',
        'gas': '#96ceb4'
      }

      /** leak漏液|matter异物|gas气体传感器|temp温度传感器 */
      const alarmType = {
        'leak': 'AI漏液检测',
        'matter': 'AI异物检测',
        'temp': '温度检测异常',
        'gas': '烟雾检测异常'
      }

      return {
        value: [ptX, ptY],
        name: alarm.name || 'AI报警',
        itemStyle: { color: iconMap[alarm.type] || '#ff6b6b' },
        symbolSize: 15,
        symbol: 'triangle', // 使用三角形图标区分AI报警
        alarm: alarm,
        alarmType: alarmType[alarm.type]
      }
    })

    // 无论是否有数据都添加系列，空数据会清空显示
    series.push({
      name: 'AI报警',
      type: 'scatter',
      data: aiAlarmData,
      tooltip: {
        formatter: (params) => {
          const alarm = params.data.alarm
          return `${params[1]}<br/>类型: ${params[6]}<br/>时间: ${alarm.time || ''}<br/>部门: ${alarm.dept || '无'}<br/>坐标: (${alarm.x.toFixed(2)}, ${alarm.y.toFixed(2)})`
        }
      }
    })
  }

  // 添加点位
  if (mapInfo.value.ps && mapInfo.value.ps.length > 0) {
    const filteredPoints = mapInfo.value.ps.filter(point => {
      const className = point[6] // class_name
      return selectedPointTypes.value.includes(className)
    })

    const pointData = filteredPoints.map(point => {
      const [xmapId, bizId, x, y, angle, isCross, className, instanceName] = point
      const ptX = calcCoordinateToPt(x, false)
      const ptY = calcCoordinateToPt(y, true)

      const colorMap = {
        'LandMark': '#409EFF',
        'ChargePoint': '#67C23A',
        'IdenPoint': '#E6A23C',
        'InfraredDetection': '#F56C6C',
        'Crossroads': '#909399',
        'Exit': '#409EFF',
        'Parking': '#67C23A'
      }

      const symbolMap = {
        'LandMark': 'pin',
        'ChargePoint': 'rect',
        'IdenPoint': 'diamond',
        'InfraredDetection': 'roundRect',
        'Crossroads': 'arrow',
        'Exit': 'triangle',
        'Parking': 'rect'
      }

      const classNameDesc = {
        'LandMark': '标记点',
        'ChargePoint': '充电点',
        'IdenPoint': '识别点',
        'InfraredDetection': '红外测温',
        'Crossroads': '十字路口',
        'Exit': '出口',
        'Parking': '停车位'
      }

      return {
        value: [ptX, ptY],
        name: instanceName || className,
        itemStyle: { color: colorMap[className] || '#409EFF' },
        symbolSize: 10,
        symbol: symbolMap[className] || 'circle',
        point: point,
        classNameDesc: classNameDesc[className]
      }
    })

    if (pointData.length > 0) {
      series.push({
        name: '点位',
        type: 'scatter',
        data: pointData,
        tooltip: {
          formatter: (params) => {
            const point = params.data.point
            return `${point[7]}<br/>类型: ${params[6]}<br/>坐标: (${point[2].toFixed(2)}, ${point[3].toFixed(2)})`
          }
        }
      })
    }
  }

  echartsInstance.value.setOption({
    series: series
  })
}

const updateRobotElements = () => {
  if (!echartsInstance.value || !robotStatusList.value || robotStatusList.value.length === 0) return

  const robotData = robotStatusList.value.filter(robot => robot.isOnPath).map(robot => {
    // 确保机器人位置数据存在
    const posX = robot.pos?.x || 0
    const posY = robot.pos?.y || 0

    const ptX = calcCoordinateToPt(posX, false)
    const ptY = calcCoordinateToPt(posY, true)

    return {
      value: [ptX, ptY],
      name: robot.name || `机器人${robot.id}`,
      itemStyle: { color: robot.clr || '#ff4444' },
      symbolSize: 20,
      symbol: 'roundRect', // 使用圆角矩形表示机器人
      robot: robot
    }
  })

  // 添加机器人系列
  const currentOption = echartsInstance.value.getOption()
  const newSeries = [...(currentOption.series || [])]

  // 移除之前的机器人系列
  const robotSeriesIndex = newSeries.findIndex(s => s.name === '机器人')
  if (robotSeriesIndex >= 0) {
    newSeries.splice(robotSeriesIndex, 1)
  }

  // 添加新的机器人系列
  newSeries.push({
    name: '机器人',
    type: 'scatter',
    data: robotData,
    symbolSize: 20,
    tooltip: {
      formatter: (params) => {
        const robot = params.data.robot
        const posX = robot.pos?.x || 0
        const posY = robot.pos?.y || 0
        return `${params[1]}<br/>状态: ${robot.s}<br/>工作模式：${robot.ws}<br/>电量: ${robot.bty}<br/>速度: ${robot.vel}m/s<br/>置信度：${robot.rel}<br/>任务：${robot.tkName}<br/>位置: (${posX.toFixed(2)}, ${posY.toFixed(2)})`
      }
    }
  })

  echartsInstance.value.setOption({
    series: newSeries
  })
}

// 坐标转换函数：将实际坐标（米）转换为像素坐标
const calcCoordinateToPt = (coord, isYAxis = false) => {
  if (!mapData.value) return coord

  const { scale = 0.0037, offsetX = 0, offsetY = 0, minPosX = 0, minPosY = 0 } = mapData.value

  // 基础转换：实际坐标转换为pt
  // scale是米到pt的转换比例，通常CAD中1米约等于72pt
  const ptScale = 1 / scale // 将scale转换为pt/米的比例

  if (isYAxis) {
    // Y轴坐标转换，需要考虑偏移
    return (coord - minPosY) * ptScale + offsetY
  } else {
    // X轴坐标转换
    return (coord - minPosX) * ptScale + offsetX
  }
}

// 坐标转换完成，移除了未使用的辅助函数

// 鼠标事件处理
const handleWheel = (event) => {
  event.preventDefault()

  const delta = event.deltaY > 0 ? -0.1 : 0.1
  const newScale = Math.max(0.1, Math.min(5, mapState.scale + delta))

  if (newScale !== mapState.scale) {
    const rect = svgContainer.value.getBoundingClientRect()
    const mouseX = event.clientX - rect.left
    const mouseY = event.clientY - rect.top

    // 计算缩放中心
    const scaleRatio = newScale / mapState.scale
    mapState.translateX = mouseX - (mouseX - mapState.translateX) * scaleRatio
    mapState.translateY = mouseY - (mouseY - mapState.translateY) * scaleRatio
    mapState.scale = newScale
  }
}

const handleMouseDown = (event) => {
  if (event.button === 0) { // 左键
    mapState.isDragging = true
    mapState.lastMouseX = event.clientX
    mapState.lastMouseY = event.clientY
    svgContainer.value.style.cursor = 'grabbing'
  }
}

const handleMouseMove = (event) => {
  if (mapState.isDragging) {
    const deltaX = event.clientX - mapState.lastMouseX
    const deltaY = event.clientY - mapState.lastMouseY

    mapState.translateX += deltaX
    mapState.translateY += deltaY
    mapState.lastMouseX = event.clientX
    mapState.lastMouseY = event.clientY
  }
}

const handleMouseUp = () => {
  mapState.isDragging = false
  svgContainer.value.style.cursor = 'grab'
}

const handleMouseLeave = () => {
  mapState.isDragging = false
  svgContainer.value.style.cursor = 'grab'
}

const handleEChartsClick = (params) => {
  console.log('点击了地图元素:', params)
}

const handlePointTypeChange = () => {
  updateMapElements()
}

// 键盘事件处理
const setupKeyboardEvents = () => {
  document.addEventListener('keydown', handleKeyDown)
}

const handleKeyDown = (event) => {
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  } else if (event.key === 'Escape' && isFullscreen.value) {
    event.preventDefault()
    exitFullscreen()
  }
}

const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    enterFullscreen()
  } else {
    exitFullscreen()
  }
}

const enterFullscreen = () => {
  isFullscreen.value = true
  mapContainer.value.classList.add('fullscreen-mode')
  document.body.style.overflow = 'hidden'
}

const exitFullscreen = () => {
  isFullscreen.value = false
  mapContainer.value.classList.remove('fullscreen-mode')
  document.body.style.overflow = ''
}

const cleanup = () => {
  if (websocket.value) {
    websocket.value.close()
  }
  if (echartsInstance.value) {
    echartsInstance.value.dispose()
  }
  document.removeEventListener('keydown', handleKeyDown)
}

// 暴露方法给父组件
defineExpose({
  loadMapData,
  toggleFullscreen,
  updateMapElements
})
</script>

<style lang="scss" scoped>
.intelligent-robot-map {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;

  .map-main-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      background: #fff;
    }
  }

  .map-display-area {
    flex: 1;
    position: relative;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
  }

  .svg-map-container {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: grab;
    overflow: hidden;

    &:active {
      cursor: grabbing;
    }
  }

  .svg-map {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    :deep(svg) {
      width: 100%;
      height: 100%;
      display: block;
    }
  }

  .echarts-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto;
    z-index: 10;
  }

  .zoom-info {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    z-index: 20;
  }

  .fullscreen-tip {
    position: absolute;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 20;
    animation: fadeInOut 3s ease-in-out;
  }

  .point-toolbar {
    background: #fff;
    border-top: 1px solid #e4e7ed;
    padding: 16px;

    .toolbar-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
    }

    .toolbar-content {
      :deep(.el-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .el-checkbox {
          margin-right: 0;

          .el-checkbox__label {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 100;

    .el-icon {
      font-size: 32px;
      color: #409eff;
      margin-bottom: 12px;
    }

    span {
      font-size: 14px;
      color: #606266;
    }
  }

  .connection-status {
    position: absolute;
    bottom: 16px;
    right: 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    z-index: 20;

    &.connected {
      background: #f0f9ff;
      color: #067f23;
      border: 1px solid #b3e5fc;

      .el-icon {
        color: #067f23;
      }
    }

    &.connecting {
      background: #fff7e6;
      color: #d46b08;
      border: 1px solid #ffd591;

      .el-icon {
        color: #d46b08;
      }
    }

    &.disconnected {
      background: #fff2f0;
      color: #cf1322;
      border: 1px solid #ffccc7;

      .el-icon {
        color: #cf1322;
      }
    }
  }
}

// 全屏模式样式
.fullscreen-mode {
  .map-display-area {
    border: none;
    border-radius: 0;
  }

  .point-toolbar {
    border-top: 1px solid #e4e7ed;
    background: #fafafa;
  }
}

// 动画
@keyframes fadeInOut {
  0%, 100% {
    opacity: 0;
  }
  10%, 90% {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .intelligent-robot-map {
    .point-toolbar {
      padding: 12px;

      .toolbar-content {
        :deep(.el-checkbox-group) {
          gap: 12px;

          .el-checkbox {
            .el-checkbox__label {
              font-size: 12px;

              .el-icon {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    .zoom-info {
      top: 12px;
      right: 12px;
      padding: 6px 10px;
      font-size: 12px;
    }

    .connection-status {
      bottom: 12px;
      right: 12px;
      padding: 4px 8px;
      font-size: 11px;
    }
  }
}
</style>
