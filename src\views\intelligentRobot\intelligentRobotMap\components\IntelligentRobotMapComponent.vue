<template>
  <div class="intelligent-robot-map" ref="mapContainer">
    <!-- 地图主容器 -->
    <div class="map-main-container" :class="{ 'fullscreen': isFullscreen }">
      <!-- 地图显示区域 -->
      <div class="map-display-area" ref="mapDisplayArea">
        <!-- SVG地图容器 -->
        <div
          class="svg-map-container"
          ref="svgContainer"
          @wheel="handleWheel"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseLeave"
        >
          <!-- SVG地图 - 使用CSS transform进行交互 -->
          <div
            class="svg-map"
            ref="svgMap"
            :style="mapTransformStyle"
            v-html="svgContent"
          ></div>

          <!-- ECharts图层 - 使用容器像素坐标系，与SVG同步变换 -->
          <div
            class="echarts-layer"
            ref="echartsContainer"
          ></div>
        </div>

        <!-- 缩放信息显示 -->
        <div class="zoom-info" v-if="svgContent">
          <span>{{ Math.round(viewState.scale * 100) }}%</span>
        </div>

        <!-- 全屏提示 -->
        <div class="fullscreen-tip" v-if="isFullscreen">
          <span>按 ESC 退出全屏</span>
        </div>
      </div>

      <!-- 点位选择工具栏 -->
      <div class="point-toolbar" v-if="!isFullscreen">
        <div class="toolbar-content">
          <el-checkbox-group v-model="selectedPointTypes" @change="handlePointTypeChange">
            <el-checkbox
              v-for="pointType in pointTypeOptions"
              :key="pointType.value"
              :label="pointType.value"
              :disabled="!mapInfo || !mapInfo.ps || mapInfo.ps.length === 0"
            >
              <el-icon><component :is="pointType.icon" /></el-icon>
              {{ pointType.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-overlay" v-if="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>{{ loadingText }}</span>
    </div>

    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="connectionStatus">
      <el-icon>
        <component :is="connectionStatus === 'connected' ? 'Connection' : 'Disconnect'" />
      </el-icon>
      <span>{{ connectionStatusText }}</span>
    </div>
  </div>
</template>

<script setup name="IntelligentRobotMapComponent">
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Loading,
//  Connection,
//  Disconnect,
//  MapLocation,
//  Charging,
//  View,
//  TakeawayBox,
//  Position,
//  Guide,
//  Right,
//  CaretRight
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getToken } from '@/utils/auth'
import { getMapInfo } from '@/api/intelligentRobot/intelligentRobotView'
import { calcNewCoordinateXToPt, calcNewCoordinateYToPt } from '@/utils/mapCalc' // 使用自定义坐标转换

// Props
const props = defineProps({
  // 可以接收外部传入的配置
})

// Emits
const emit = defineEmits(['map-loaded', 'websocket-connected', 'websocket-disconnected'])

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const baseWsUrl = import.meta.env.VITE_WS_URL;

// Refs
const mapContainer = ref(null)
const mapDisplayArea = ref(null)
const svgContainer = ref(null)
const svgMap = ref(null)
const echartsContainer = ref(null)

// 响应式数据
const loading = ref(false)
const loadingText = ref('正在加载地图...')
const svgContent = ref('')
const isFullscreen = ref(false)
const echartsInstance = ref(null)

// WebSocket相关
const websocket = ref(null)
const connectionStatus = ref('disconnected') // connected, connecting, disconnected
const isComponentUnmounted = ref(false) // 组件卸载标志
const reconnectTimer = ref(null) // 重连定时器引用
const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return 'WebSocket已连接'
    case 'connecting': return 'WebSocket连接中...'
    case 'disconnected': return 'WebSocket未连接'
    default: return 'WebSocket状态未知'
  }
})

// 地图状态 - 参考CenterPanelMain.vue的设计
const mapState = reactive({
  scale: 1,
  translateX: 0,
  translateY: 0,
  isDragging: false,
  lastMouseX: 0,
  lastMouseY: 0
})

// 基础变量 - 参考CenterPanelMain.vue
const cadWidthPx = ref(0)
const cadHeightPx = ref(0)
const cadWidth = ref(0)
const cadHeight = ref(0)
const pt2px = ref(96 / 72)
const baseScale = ref(1)
const baseX = ref(0)
const baseY = ref(0)

// 视图状态 - 用于记忆缩放和平移（对应CenterPanelMain的viewState）
const viewState = reactive({
  scale: 1,
  translateX: 0,
  translateY: 0
})

// CAD变换状态（对应CenterPanelMain的cadTransform）
const cadTransform = reactive({
  scale: 1,
  translateX: 0,
  translateY: 0,
  rotation: 0
})

// 地图数据
// mapData json: {"cadUrl":"/profile/upload/???/?.svg", "scale":0.0037, "offsetX":0, "offsetY":0,
//                "minPosX":0, "maxPosX":100, "minPosY":0, "maxPosY":100,
//                "cadWidth":26.89, "cadHeight":26.89, "xmapWidth":100, "xmapHeight":100}
// cadWidth/cadHeight: CAD图纸的原始尺寸（pt为单位）
// xmapWidth/xmapHeight: 物理原始尺寸（米为单位）
// minPosX/maxPosX: xmap物理地图的左下角坐标
// maxPosX/maxPosY: xmap物理地图的右上角坐标
// scale: 统一比例尺
// offsetX/offsetY: xmap(左上角)坐标与cad图纸左上角的二次偏移量（pt为单位）
const mapData = ref(null)
const mapInfo = ref(null) // WebSocket推送的地图信息
const robotStatusList = ref([]) // 机器人状态列表

// 点位类型选项
const pointTypeOptions = ref([
  { label: '标记点', value: 'LandMark', icon: 'MapLocation' },
  { label: '充电点', value: 'ChargePoint', icon: 'Charging' },
  { label: '识别点', value: 'IdenPoint', icon: 'View' },
  { label: '红外测温', value: 'InfraredDetection', icon: 'TakeawayBox' },
  { label: '十字路口', value: 'Crossroads', icon: 'Position' },
  { label: '出口', value: 'Exit', icon: 'Right' },
  { label: '停车位', value: 'Parking', icon: 'CaretRight' }
])

const selectedPointTypes = ref(['LandMark', 'ChargePoint', 'IdenPoint']) // 默认选中的点位类型

// SVG地图的CSS transform样式 - 用于用户交互
const mapTransformStyle = computed(() => {
  return {
    transform: `translate(${viewState.translateX}px, ${viewState.translateY}px) scale(${viewState.scale})`,
    transformOrigin: '0 0',
    transition: mapState.isDragging ? 'none' : 'transform 0.2s ease-out'
  }
})

// 生命周期
onMounted(() => {
  initComponent()
  setupKeyboardEvents()
})

onBeforeUnmount(() => {
  // 设置组件卸载标志，防止重连
  isComponentUnmounted.value = true
  cleanup()
})

// 监听点位类型变化
watch(selectedPointTypes, () => {
  updateEchartsLayers()
}, { deep: true })

// 监听地图信息变化
watch(mapInfo, () => {
  updateEchartsLayers()
}, { deep: true })

// 监听机器人状态变化
watch(robotStatusList, () => {
  updateRobotElements()
}, { deep: true })

// 方法
const initComponent = async () => {
  try {
    await loadMapData()
    await initWebSocket()
    initECharts()
  } catch (error) {
    console.error('初始化组件失败:', error)
    ElMessage.error('初始化地图组件失败')
  }
}

const loadMapData = async () => {
  loading.value = true
  loadingText.value = '正在加载地图数据...'

  try {
    const query = {"mapId": -1}
    const response = await getMapInfo(query)
    if (response.code === 200 && response.data) {
      mapData.value = response.data
      console.log('地图数据:', mapData.value)

      if (response.data.cadUrl) {
        // 构建完整的SVG URL
        const fullSvgUrl = baseUrl + response.data.cadUrl

        // 加载SVG内容
        await loadSvgContent(fullSvgUrl)

        // 地图加载完成后重新初始化ECharts以使用正确的坐标系
        if (echartsInstance.value) {
          echartsInstance.value.dispose()
        }
        await nextTick()
        initECharts()

        // 强制刷新SVG显示
        await nextTick()
        if (svgMap.value && svgContent.value) {
          // 重新设置innerHTML以确保SVG正确渲染
          svgMap.value.innerHTML = svgContent.value
          console.log('强制刷新SVG显示完成')
        }

        ElMessage.success('地图加载成功')
        emit('map-loaded', mapData.value)
      } else {
        ElMessage.warning('地图数据为空')
      }
    } else {
      throw new Error(response.msg || '获取地图数据失败')
    }
  } catch (error) {
    console.error('加载地图数据失败:', error)
    ElMessage.error('加载地图数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 预加载图片并计算尺寸 - 参考CenterPanelMain.vue
const preloadImages = () => {
  const promises = []

  if (mapData.value && mapData.value.cadUrl) {
    const fullSvgUrl = baseUrl + mapData.value.cadUrl

    const cadPromise = new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        cadWidthPx.value = img.width
        cadHeightPx.value = img.height

        const cadWidthPt = mapData.value && mapData.value.cadWidth
        const cadHeightPt = mapData.value && mapData.value.cadHeight

        if (cadWidthPt && cadHeightPt) {
          pt2px.value = Math.max(
            cadWidthPx.value / cadWidthPt,
            cadHeightPx.value / cadHeightPt
          )
          cadWidth.value = cadWidthPt
          cadHeight.value = cadHeightPt
        } else {
          pt2px.value = 96 / 72
          cadWidth.value = cadWidthPx.value / pt2px.value
          cadHeight.value = cadHeightPx.value / pt2px.value
        }

        console.log('CAD图片尺寸:', cadWidthPx.value, 'x', cadHeightPx.value)
        console.log('CAD pt尺寸:', cadWidth.value, 'x', cadHeight.value)
        console.log('pt2px比例:', pt2px.value)

        resolve()
      }
      img.onerror = () => {
        console.error('CAD图层加载失败')
        resolve()
      }
      img.src = fullSvgUrl
    })
    promises.push(cadPromise)
  }

  return Promise.all(promises)
}

// 加载SVG内容
const loadSvgContent = async (svgUrl) => {
  try {
    console.log('开始加载SVG:', svgUrl)
    const response = await fetch(svgUrl)
    if (response.ok) {
      const svgText = await response.text()
      svgContent.value = svgText
      console.log('SVG内容加载成功，长度:', svgText.length)

      // 预加载图片并计算尺寸
      await preloadImages()

      // 等待DOM更新后检查SVG是否正确渲染
      await nextTick()
      if (svgMap.value) {
        console.log('SVG DOM元素:', svgMap.value)
        console.log('SVG容器尺寸:', svgMap.value.clientWidth, 'x', svgMap.value.clientHeight)
        console.log('SVG innerHTML长度:', svgMap.value.innerHTML.length)
        console.log('SVG内容前100字符:', svgText.substring(0, 100))

        // 检查SVG元素是否正确创建
        const svgElement = svgMap.value.querySelector('svg')
        if (svgElement) {
          console.log('找到SVG元素:', svgElement)
          console.log('SVG元素尺寸:', svgElement.clientWidth, 'x', svgElement.clientHeight)
          console.log('SVG viewBox:', svgElement.getAttribute('viewBox'))
        } else {
          console.error('未找到SVG元素！')
        }
      }
    } else {
      throw new Error(`加载SVG失败: ${response.status}`)
    }
  } catch (error) {
    console.error('加载SVG内容失败:', error)
    ElMessage.error('加载SVG内容失败: ' + error.message)
  }
}

const initWebSocket = async () => {
  try {
    connectionStatus.value = 'connecting'
    const token = getToken()
    const wsUrl = `${baseWsUrl}/wsDispatch?token=${encodeURIComponent(token)}`

    websocket.value = new WebSocket(wsUrl)

    websocket.value.onopen = () => {
      connectionStatus.value = 'connected'
      ElMessage.success('WebSocket连接成功')
      emit('websocket-connected')
    }

    websocket.value.onmessage = (event) => {
      handleWebSocketMessage(event)
    }

    websocket.value.onclose = () => {
      connectionStatus.value = 'disconnected'
      ElMessage.warning('WebSocket连接已断开')
      emit('websocket-disconnected')

      // 尝试重连，但需要检查组件是否已卸载
      if (!isComponentUnmounted.value) {
        reconnectTimer.value = setTimeout(() => {
          // 双重检查：确保组件未卸载且连接状态仍为断开
          if (!isComponentUnmounted.value && connectionStatus.value === 'disconnected') {
            initWebSocket()
          }
        }, 5000)
      }
    }

    websocket.value.onerror = (error) => {
      console.error('WebSocket错误:', error)
      connectionStatus.value = 'disconnected'
      ElMessage.error('WebSocket连接错误')
    }
  } catch (error) {
    console.error('初始化WebSocket失败:', error)
    connectionStatus.value = 'disconnected'
  }
}

const handleWebSocketMessage = (event) => {
  try {
    const payload = JSON.parse(event.data)
    console.log('收到WebSocket消息:', payload)

    switch (payload.type) {
      case 'MAP':
        // 地图信息更新
        mapInfo.value = payload.data
        console.log('地图信息更新:', mapInfo.value)
        break

      case 'ROBOT':
        // 机器人状态更新
        robotStatusList.value = payload.data || []
        console.log('机器人状态更新:', robotStatusList.value)
        break

      case 'OBSTACLE':
        // 障碍变更
        updateObstacles(payload.data)
        break

      case 'AI_ALARM':
        // AI报警变更
        updateAiAlarms(payload.data)
        break

      default:
        console.log('未知消息类型:', payload.type)
    }
  } catch (error) {
    console.error('处理WebSocket消息失败:', error)
  }
}

const updateObstacles = (obstacles) => { // obstacles结构: [{"x":0.0,"y":0.0,"xmapId":"1","robotId":1,"type":0}]
  if (!mapInfo.value) return

  // 直接用新的障碍数据替换原有数据
  if (Array.isArray(obstacles)) {
    // 如果obstacles为空数组[]，说明所有障碍都已消除
    // 如果obstacles有数据，则更新为最新的障碍列表
    mapInfo.value.o = obstacles

    console.log('障碍数据更新:', obstacles.length > 0 ? `${obstacles.length}个障碍` : '所有障碍已清除')

    // 立即刷新地图显示
    // 不需要这句话，因为mapInfo.value变化时，会自动调用updateMapElements()
    // updateMapElements()
  }
}

const updateAiAlarms = (aiAlarms) => { // aiAlarms结构: [{"name":"漏液报警","x":1.0,"y":2.0,"type":"leak","time":"2025-01-06 10:30:00","dept":"ETCH"}]
  if (!mapInfo.value) return

  // 直接用新的AI报警数据替换原有数据
  if (Array.isArray(aiAlarms)) {
    // 如果aiAlarms为空数组[]，说明所有AI报警都已消除
    // 如果aiAlarms有数据，则更新为最新的AI报警列表
    mapInfo.value.ai = aiAlarms

    console.log('AI报警数据更新:', aiAlarms.length > 0 ? `${aiAlarms.length}个AI报警` : '所有AI报警已确认')

    // 立即刷新地图显示
    // 不需要这句话，因为mapInfo.value变化时，会自动调用updateMapElements()
    // updateMapElements()
  }
}

const initECharts = () => {
  if (!echartsContainer.value) return

  // 检查是否已经初始化，避免重复初始化
  if (echartsInstance.value) {
    echartsInstance.value.dispose()
  }

  echartsInstance.value = echarts.init(echartsContainer.value, null, {
    renderer: 'canvas'
  })

  // 初始化后立即更新图层
  updateEchartsLayers()

  // 监听点击事件
  echartsInstance.value.on('click', handleEChartsClick)
}

// 更新ECharts图层 - 参考CenterPanelMain.vue的updateEchartsLayers
const updateEchartsLayers = () => {
  if (!echartsInstance.value) return

  const containerWidth = echartsContainer.value.clientWidth
  const containerHeight = echartsContainer.value.clientHeight

  // 计算CAD图层的基础缩放 - 参考CenterPanelMain.vue
  let cadScale = 1
  if (cadWidthPx.value && cadHeightPx.value) {
    const scaleX = containerWidth / cadWidthPx.value
    const scaleY = containerHeight / cadHeightPx.value
    cadScale = Math.min(scaleX, scaleY)
  }

  // 设置ECharts配置 - 使用容器像素坐标系（与CenterPanelMain.vue一致）
  const option = {
    backgroundColor: 'transparent', // 透明背景，不遮挡SVG层
    animation: false,
    hoverLayerThreshold: Infinity,
    grid: {
      show: false,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      containLabel: false
    },
    xAxis: {
      show: false,
      type: 'value',
      min: 0,
      max: containerWidth
    },
    yAxis: {
      show: false,
      type: 'value',
      min: 0,
      max: containerHeight
    },
    tooltip: {
      trigger: 'item',
      confine: true,
      enterable: true,
      appendToBody: true,
      backgroundColor: 'rgba(50,50,50,0.9)',
      borderColor: '#333',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#fff'
      }
    },
    series: [],
    graphic: []
  }

  // 计算基础变量 - 参考CenterPanelMain.vue
  if (cadWidthPx.value && cadHeightPx.value) {
    const centeredX = (containerWidth - cadWidthPx.value * cadScale) / 2
    const centeredY = (containerHeight - cadHeightPx.value * cadScale) / 2

    baseScale.value = cadScale
    baseX.value = centeredX
    baseY.value = centeredY
  }

  // 添加地图元素
  updateMapElements(option, containerHeight)

  echartsInstance.value.setOption(option, true)
}

// 更新地图元素 - 参考CenterPanelMain.vue的addWorkPointsAndPaths
const updateMapElements = (option, containerHeight) => {
  if (!mapInfo.value || !mapData.value) return

  // 混合架构坐标转换：
  // SVG层：使用CSS transform，原点在左上角(0,0)
  // ECharts层：需要模拟SVG的transform行为

  // 基础变换（不包含viewState）
  const baseActiveScale = baseScale.value * cadTransform.scale
  const baseActiveTranslateX = baseX.value + cadTransform.translateX
  const baseActiveTranslateY = baseY.value + cadTransform.translateY

  // 清空现有系列
  option.series = []

  // 添加障碍点 - 即使为空数组也要处理，以便清空显示
  if (mapInfo.value.o) {
    const obstacleData = []

    mapInfo.value.o
      .filter(obstacle => obstacle.type === 0) // 只显示普通障碍
      .forEach((obstacle) => {
        // 使用mapCalc工具转换为pt坐标
        const ptX = calcNewCoordinateXToPt(obstacle.x, mapData.value.minPosX, mapData.value.scale, mapData.value.offsetX)
        const ptY = calcNewCoordinateYToPt(obstacle.y, mapData.value.maxPosY, mapData.value.scale, mapData.value.offsetY)

        // 混合架构坐标转换：模拟SVG的transform行为
        // 1. 首先将pt单位转换为px单位
        const pointXPx = ptX * pt2px.value
        const pointYPx = ptY * pt2px.value

        // 2. 应用基础变换（不包含viewState）
        const baseX = baseActiveTranslateX + pointXPx * baseActiveScale
        const baseY = baseActiveTranslateY + pointYPx * baseActiveScale

        // 3. 模拟SVG的CSS transform: translate(tx, ty) scale(s)
        // SVG transform-origin是(0,0)，所以先缩放再平移
        const finalX = baseX * viewState.scale + viewState.translateX
        const finalY = containerHeight - (baseY * viewState.scale + viewState.translateY) // Y轴翻转

        obstacleData.push({
          value: [finalX, finalY],
          name: `障碍点`,
          itemStyle: { color: '#ff4444' },
          symbolSize: 12,
          symbol: 'circle',
          obstacle: obstacle
        })
      })

    // 无论是否有数据都添加系列，空数据会清空显示
    option.series.push({
      name: '障碍点',
      type: 'scatter',
      data: obstacleData,
      tooltip: {
        formatter: (params) => {
          const obstacle = params.data.obstacle
          return `障碍点<br/>ID: ${obstacle.xmapId}<br/>坐标: (${obstacle.x.toFixed(2)}, ${obstacle.y.toFixed(2)})`
        }
      }
    })
  }

  // 添加AI报警点 - 即使为空数组也要处理，以便清空显示
  if (mapInfo.value.ai) {
    const aiAlarmData = mapInfo.value.ai.map(alarm => {
      // 使用mapCalc工具转换为pt坐标
      const ptX = calcNewCoordinateXToPt(alarm.x, mapData.value.minPosX, mapData.value.scale, mapData.value.offsetX)
      const ptY = calcNewCoordinateYToPt(alarm.y, mapData.value.maxPosY, mapData.value.scale, mapData.value.offsetY)

      // 混合架构坐标转换：模拟SVG的transform行为
      // 1. 首先将pt单位转换为px单位
      const pointXPx = ptX * pt2px.value
      const pointYPx = ptY * pt2px.value

      // 2. 应用基础变换（不包含viewState）
      const baseX = baseActiveTranslateX + pointXPx * baseActiveScale
      const baseY = baseActiveTranslateY + pointYPx * baseActiveScale

      // 3. 模拟SVG的CSS transform: translate(tx, ty) scale(s)
      const finalX = baseX * viewState.scale + viewState.translateX
      const finalY = containerHeight - (baseY * viewState.scale + viewState.translateY) // Y轴翻转

      const iconMap = {
        'leak': '#ff6b6b',
        'matter': '#4ecdc4',
        'temp': '#45b7d1',
        'gas': '#96ceb4'
      }

      /** leak漏液|matter异物|gas气体传感器|temp温度传感器 */
      const alarmType = {
        'leak': 'AI漏液检测',
        'matter': 'AI异物检测',
        'temp': '温度检测异常',
        'gas': '烟雾检测异常'
      }

      return {
        value: [finalX, finalY],
        name: alarm.name || 'AI报警',
        itemStyle: { color: iconMap[alarm.type] || '#ff6b6b' },
        symbolSize: 15,
        symbol: 'triangle', // 使用三角形图标区分AI报警
        alarm: alarm,
        alarmType: alarmType[alarm.type]
      }
    })

    // 无论是否有数据都添加系列，空数据会清空显示
    option.series.push({
      name: 'AI报警',
      type: 'scatter',
      data: aiAlarmData,
      tooltip: {
        formatter: (params) => {
          const alarm = params.data.alarm
          return `${params[1]}<br/>类型: ${params[6]}<br/>时间: ${alarm.time || ''}<br/>部门: ${alarm.dept || '无'}<br/>坐标: (${alarm.x.toFixed(2)}, ${alarm.y.toFixed(2)})`
        }
      }
    })
  }

  // 添加点位
  console.log("点位数量:", mapInfo.value.ps ? mapInfo.value.ps.length : 0)
  if (mapInfo.value.ps && mapInfo.value.ps.length > 0) {
    const filteredPoints = mapInfo.value.ps.filter(point => {
      const className = point[6] // class_name
      return selectedPointTypes.value.includes(className)
    })

    const pointData = filteredPoints.map(point => {
      const [, , x, y, , , className, instanceName] = point

      // 使用mapCalc工具转换为pt坐标
      const ptX = calcNewCoordinateXToPt(x, mapData.value.minPosX, mapData.value.scale, mapData.value.offsetX)
      const ptY = calcNewCoordinateYToPt(y, mapData.value.maxPosY, mapData.value.scale, mapData.value.offsetY)

      // 混合架构坐标转换：模拟SVG的transform行为
      // 1. 首先将pt单位转换为px单位
      const pointXPx = ptX * pt2px.value
      const pointYPx = ptY * pt2px.value

      // 2. 应用基础变换（不包含viewState）
      const baseX = baseActiveTranslateX + pointXPx * baseActiveScale
      const baseY = baseActiveTranslateY + pointYPx * baseActiveScale

      // 3. 模拟SVG的CSS transform: translate(tx, ty) scale(s)
      const finalX = baseX * viewState.scale + viewState.translateX
      const finalY = containerHeight - (baseY * viewState.scale + viewState.translateY) // Y轴翻转

      const colorMap = {
        'LandMark': '#909399',
        'ChargePoint': '#FF0000',
        'IdenPoint': '#FF6E00',
        'InfraredDetection': '#F56C6C',
        'Crossroads': '#909399',
        'Exit': '#409EFF',
        'Parking': '#F56C6C'
      }

      const symbolMap = {
        'LandMark': 'path://M522.528821 24.996103A393.058462 393.058462 0 0 0 243.186872 137.819897 392.664615 392.664615 0 0 0 125.846974 415.245128a377.409641 377.409641 0 0 0 71.548718 224.282257c36.890256 52.696615 116.184615 126.687179 166.386872 184.582564 50.228513 55.401026 137.531077 166.203077 156.041846 187.286974 2.70441 2.573128 2.70441 2.573128 5.303795 0 18.510769-23.709538 134.957949-160.899282 148.138667-179.383795 15.780103-18.379487 121.619692-126.60841 166.518154-184.582564a389.697641 389.697641 0 0 0 76.72123-234.679795C914.353231 197.185641 738.225231 23.762051 522.502564 24.996103z m0 551.253333a167.988513 167.988513 0 0 1-169.222565-166.071795A167.909744 167.909744 0 0 1 522.502564 244.07959a167.988513 167.988513 0 0 1 169.353846 166.098051c-1.129026 92.501333-76.826256 166.859487-169.353846 166.071795z',
        'ChargePoint': 'path://M772.425 124.088c10.799 0 19.998 11.799 19.998 25.697v750.227H232.477V149.885c0-13.998 9.2-25.697 19.998-25.697h519.95m0-60.094h-519.95c-44.195 0-79.992 38.396-79.992 85.691v750.227H64.493v59.994h895.913v-59.994h-107.99V149.885c0-47.395-35.796-85.791-79.991-85.791z m-89.992 179.982v79.992H342.467v-79.992h339.966m59.994-59.994H282.472v199.98h459.955v-199.98zM392.462 683.033H512.45V443.057zM512.45 613.04v239.977L632.438 613.04z',
        'IdenPoint': 'path://M511.06304 14.11584c-206.42304 0-373.76 167.33696-373.76 373.76s167.33696 373.76 373.76 373.76 373.76-167.33696 373.76-373.76-167.33184-373.76-373.76-373.76z m0 624.33792c-138.38848 0-250.5728-112.18432-250.5728-250.5728s112.18432-250.5728 250.5728-250.5728 250.5728 112.18432 250.5728 250.5728-112.1792 250.5728-250.5728 250.5728z M259.23584 663.50592l514.96448-10.33216-269.65504 349.88032zM513.28 273.65888c-104.19712 0-216.33024 112.90112-216.33024 112.90112s112.13312 112.90112 216.33024 112.90112c104.19712 0 216.33024-112.90112 216.33024-112.90112s-112.13312-112.90112-216.33024-112.90112z m2.304 190.52544c-43.9296 0-79.5392-35.6096-79.5392-79.5392S471.6544 305.1008 515.584 305.1008c43.92448 0 79.5392 35.6096 79.5392 79.5392s-35.61472 79.54432-79.5392 79.54432z M515.584 384.64m-49.57184 0a49.57184 49.57184 0 1 0 99.14368 0 49.57184 49.57184 0 1 0-99.14368 0Z',
        'InfraredDetection': 'path://M448 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM320 96a96 96 0 1 1 192 0A96 96 0 1 1 320 96zM144 64c-26.5 0-48 21.5-48 48l0 164.5c0 17.3-7.1 31.9-15.3 42.5C70.2 332.6 64 349.5 64 368c0 44.2 35.8 80 80 80s80-35.8 80-80c0-18.5-6.2-35.4-16.7-48.9c-8.2-10.6-15.3-25.2-15.3-42.5L192 112c0-26.5-21.5-48-48-48zM32 112C32 50.2 82.1 0 144 0s112 50.1 112 112l0 164.4c0 .1 .1 .3 .2 .6c.2 .6 .8 1.6 1.7 2.8c18.9 24.4 30.1 55 30.1 88.1c0 79.5-64.5 144-144 144S0 447.5 0 368c0-33.2 11.2-63.8 30.1-88.1c.9-1.2 1.5-2.2 1.7-2.8c.1-.3 .2-.5 .2-.6L32 112zM192 368c0 26.5-21.5 48-48 48s-48-21.5-48-48c0-20.9 13.4-38.7 32-45.3l0-50.7c0-8.8 7.2-16 16-16s16 7.2 16 16l0 50.7c18.6 6.6 32 24.4 32 45.3z',
        'Crossroads': 'path://M682.662 1024H341.338V820.454c0-69.533-68.38-137.777-138.053-137.777H0.015V341.323h203.516c69.548 0 137.807-67.635 137.807-136.548V0h341.324v209.66c0 66.451 67.346 131.663 135.964 131.663h205.359v341.354h-207.14c-63.951 0-134.183 70.612-134.183 134.92V1024zM580.273 682.677v102.392h68.26V682.677z m-102.386 0v102.392h68.258V682.677h-68.258z m-102.423 0v102.392h68.259V682.677h-68.255zM682.66 580.255v68.258h102.422v-68.255z m-443.711 0v68.258h102.39v-68.255H238.95zM682.662 477.87v68.26h102.422v-68.26z m-443.712 0v68.26h102.388v-68.26H238.95z m443.712-102.387v68.262h102.422v-68.262z m-443.712 0v68.262h102.388v-68.262H238.95z m341.323-136.547v102.387h68.26V238.935z m-102.386 0v102.387h68.258V238.935h-68.258z m-102.423 0v102.387h68.259V238.935h-68.255z',
        'Exit': 'path://M700.6 676.7l200.8-157.4c5.2-4 5.2-10.6 0-14.6L700.6 347.3c-2.6-2-6-3-9.4-3v115H489.6c-12.3 0-22.3 10-22.3 22.3v60.8c0 12.3 10 22.3 22.3 22.3h201.6v115c3.4 0 6.8-1 9.4-3zM588.8 788H205.3V236h383.5v63.2H730V94.7H64v834.6h666V729.5H588.8V788zM960 441.4l-156.3-122-19.9 25.6 156.3 122 19.9-25.6zM783.8 675.9l19.9 25.6 156.3-122-19.9-25.6-156.3 122z',
        'Parking': 'path://M511.948876484375 139.40946054687504c-157.07214534374995 0-284.3535594375 125.56139498437499-284.3535594375 280.500716671875s127.28141409374999 280.43191575 284.3535594375 280.43191575 284.3535594375-125.56139498437499 284.3535594375-280.43191575S669.021021828125 139.40946054687504 511.948876484375 139.40946054687504zM178.19636735937507 660.0248442968751c-5.0912564062499985-6.880076437499998-12.1089346875-16.099379296875-17.7505965-25.38748209375C120.67892857812512 571.684663390625 94.32823554687502 499.58146165625 94.32823554687502 419.91017721875005 94.32823554687502 192.38604832812507 281.2599130625001 8 511.8800755625 8s417.6206399531249 184.386048328125 417.6206399531249 411.91017721874994a404.47969439062496 404.47969439062496 0 0 1-66.66794071874999 222.364070578125 264.3325374375 264.3325374375 0 0 1-28.621117546874995 30.685140281249996C822.7907298124999 685.3435264531252 555.29335784375 993.1581461093749 555.29335784375 993.1581461093749c-11.214524671875 13.760152874999996-23.461060218749996 22.841853890624996-36.80840953124999 22.841853890624996s-23.323459359375-10.526517421874997-33.505972171875-22.841853890624996c0 0-301.69135237499995-326.04682260937494-306.78260878125-333.1333018125z M518.828952921875 334.1844244531251h-26.213091187499998v90.67940732812497h26.213091187499998q52.77018599999999 0 52.77018599999999-45.890109562499994-0.344003625-44.78929776562499-52.77018599999999-44.78929776562499z M511.948876484375 194.1060674375001a225.46010517187497 225.46010517187497 0 1 0 225.46010517187497 225.46010517187497A225.46010517187497 225.46010517187497 0 0 0 511.948876484375 194.1060674375001z m90.61060640624999 251.67319635937494a115.99808849999998 115.99808849999998 0 0 1-79.12087903124997 24.905877609374997h-30.822742124999998v97.903487390625H432.827997453125V287.812709421875h96.32107012499998q104.78356382812498 0 104.78356382812498 88.821786375a85.03774453124998 85.03774453124998 0 0 1-31.373148515624994 69.144768z',
      }

      const classNameDesc = {
        'LandMark': '标记点',
        'ChargePoint': '充电点',
        'IdenPoint': '识别点',
        'InfraredDetection': '红外测温',
        'Crossroads': '十字路口',
        'Exit': '出口',
        'Parking': '停车位'
      }

      return {
        value: [finalX, finalY],
        name: instanceName || className,
        itemStyle: { color: colorMap[className] || '#409EFF' },
        symbolSize: 15,
        symbol: symbolMap[className] || 'circle',
        point: point,
        classNameDesc: classNameDesc[className]
      }
    })

    option.series.push({
      name: '点位',
      type: 'scatter',
      data: pointData,
      tooltip: {
        formatter: (params) => {
          const point = params.data.point
          return `${point[7]}<br/>类型: ${params[6]}<br/>坐标: (${point[2].toFixed(2)}, ${point[3].toFixed(2)})`
        }
      }
    })
  }

  // 不需要在这里设置option，因为option已经在updateEchartsLayers中设置了
}

const updateRobotElements = () => {
  if (!echartsInstance.value || !robotStatusList.value || robotStatusList.value.length === 0) return

  const containerHeight = echartsContainer.value.clientHeight

  // 基础变换（不包含viewState）
  const baseActiveScale = baseScale.value * cadTransform.scale
  const baseActiveTranslateX = baseX.value + cadTransform.translateX
  const baseActiveTranslateY = baseY.value + cadTransform.translateY

  const robotData = robotStatusList.value.filter(robot => robot.isOnPath).map(robot => {
    // 确保机器人位置数据存在
    const posX = robot.pos?.x || 0
    const posY = robot.pos?.y || 0

    // 使用mapCalc工具转换为pt坐标
    const ptX = calcNewCoordinateXToPt(posX, mapData.value.minPosX, mapData.value.scale, mapData.value.offsetX)
    const ptY = calcNewCoordinateYToPt(posY, mapData.value.maxPosY, mapData.value.scale, mapData.value.offsetY)

    // 混合架构坐标转换：模拟SVG的transform行为
    // 1. 首先将pt单位转换为px单位
    const pointXPx = ptX * pt2px.value
    const pointYPx = ptY * pt2px.value

    // 2. 应用基础变换（不包含viewState）
    const baseX = baseActiveTranslateX + pointXPx * baseActiveScale
    const baseY = baseActiveTranslateY + pointYPx * baseActiveScale

    // 3. 模拟SVG的CSS transform: translate(tx, ty) scale(s)
    const finalX = baseX * viewState.scale + viewState.translateX
    const finalY = containerHeight - (baseY * viewState.scale + viewState.translateY) // Y轴翻转

    return {
      value: [finalX, finalY],
      name: robot.name || `机器人${robot.id}`,
      itemStyle: { color: robot.clr || '#ff4444' },
      symbolSize: 20,
      symbol: 'roundRect', // 使用圆角矩形表示机器人
      robot: robot
    }
  })

  // 添加机器人系列
  const currentOption = echartsInstance.value.getOption()
  const newSeries = [...(currentOption.series || [])]

  // 移除之前的机器人系列
  const robotSeriesIndex = newSeries.findIndex(s => s.name === '机器人')
  if (robotSeriesIndex >= 0) {
    newSeries.splice(robotSeriesIndex, 1)
  }

  // 添加新的机器人系列
  newSeries.push({
    name: '机器人',
    type: 'scatter',
    data: robotData,
    symbolSize: 20,
    tooltip: {
      formatter: (params) => {
        const robot = params.data.robot
        const posX = robot.pos?.x || 0
        const posY = robot.pos?.y || 0
        return `${params[1]}<br/>状态: ${robot.s}<br/>工作模式：${robot.ws}<br/>电量: ${robot.bty}<br/>速度: ${robot.vel}m/s<br/>置信度：${robot.rel}<br/>任务：${robot.tkName}<br/>位置: (${posX.toFixed(2)}, ${posY.toFixed(2)})`
      }
    }
  })

  echartsInstance.value.setOption({
    series: newSeries
  })
}

// 删除旧的坐标转换函数，现在直接使用CenterPanelMain.vue的算法

// 坐标转换完成，移除了未使用的辅助函数

// 鼠标事件处理
const handleWheel = (event) => {
  event.preventDefault()

  const zoomConfig = {
    step: 0.1,
    min: 0.1,
    max: 10
  }

  const rect = svgContainer.value.getBoundingClientRect()
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top

  // 计算缩放方向和大小
  const delta = event.deltaY > 0 ? -zoomConfig.step : zoomConfig.step
  const newScale = Math.max(
    zoomConfig.min,
    Math.min(zoomConfig.max, viewState.scale + delta)
  )

  // 如果缩放没有变化，不进行操作
  if (newScale === viewState.scale) return

  // 计算缩放中心点相对于图像的位置
  const imageX = mouseX - viewState.translateX
  const imageY = mouseY - viewState.translateY

  // 计算新的平移量，保持鼠标位置不变
  const scaleFactor = newScale / viewState.scale
  const newTranslateX = mouseX - imageX * scaleFactor
  const newTranslateY = mouseY - imageY * scaleFactor

  // 更新视图状态
  viewState.scale = newScale
  viewState.translateX = newTranslateX
  viewState.translateY = newTranslateY

  // 更新图层显示
  updateEchartsLayers()
}

const handleMouseDown = (event) => {
  if (event.button === 0) { // 左键
    mapState.isDragging = true
    mapState.lastMouseX = event.clientX
    mapState.lastMouseY = event.clientY
    svgContainer.value.style.cursor = 'grabbing'
  }
}

const handleMouseMove = (event) => {
  if (mapState.isDragging) {
    const deltaX = event.clientX - mapState.lastMouseX
    const deltaY = event.clientY - mapState.lastMouseY

    // 更新鼠标位置
    mapState.lastMouseX = event.clientX
    mapState.lastMouseY = event.clientY

    // 更新视图状态
    viewState.translateX += deltaX
    viewState.translateY += deltaY

    // 更新图层显示
    updateEchartsLayers()
  }
}

const handleMouseUp = () => {
  mapState.isDragging = false
  svgContainer.value.style.cursor = 'grab'
}

const handleMouseLeave = () => {
  mapState.isDragging = false
  svgContainer.value.style.cursor = 'grab'
}

const handleEChartsClick = (params) => {
  console.log('点击了地图元素:', params)
}

const handlePointTypeChange = () => {
  updateEchartsLayers()
}

// 键盘事件处理
const setupKeyboardEvents = () => {
  document.addEventListener('keydown', handleKeyDown)
}

const handleKeyDown = (event) => {
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  } else if (event.key === 'Escape' && isFullscreen.value) {
    event.preventDefault()
    exitFullscreen()
  }
}

const toggleFullscreen = () => {
  if (!isFullscreen.value) {
    enterFullscreen()
  } else {
    exitFullscreen()
  }
}

const enterFullscreen = () => {
  isFullscreen.value = true
  mapContainer.value.classList.add('fullscreen-mode')
  document.body.style.overflow = 'hidden'
}

const exitFullscreen = () => {
  isFullscreen.value = false
  mapContainer.value.classList.remove('fullscreen-mode')
  document.body.style.overflow = ''
}

const cleanup = () => {
  // 清理重连定时器
  if (reconnectTimer.value) {
    clearTimeout(reconnectTimer.value)
    reconnectTimer.value = null
  }

  // 关闭WebSocket连接
  if (websocket.value) {
    websocket.value.close()
    websocket.value = null
  }

  // 清理ECharts实例
  if (echartsInstance.value) {
    echartsInstance.value.dispose()
    echartsInstance.value = null
  }

  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeyDown)
}

// 暴露方法给父组件
defineExpose({
  loadMapData,
  toggleFullscreen,
  updateMapElements
})
</script>

<style lang="scss" scoped>
.intelligent-robot-map {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;

  .map-main-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9999;
      background: #fff;
    }
  }

  .map-display-area {
    flex: 1;
    position: relative;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
  }

  .svg-map-container {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: grab;
    overflow: hidden;

    &:active {
      cursor: grabbing;
    }
  }

  .svg-map {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background: transparent;
    z-index: 1;

    :deep(svg) {
      width: 100%;
      height: 100%;
      display: block;
      max-width: none;
      max-height: none;
      object-fit: contain;
    }
  }

  .echarts-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto;
    z-index: 10;
  }

  .zoom-info {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    z-index: 20;
  }

  .fullscreen-tip {
    position: absolute;
    top: 16px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 20;
    animation: fadeInOut 3s ease-in-out;
  }

  .point-toolbar {
    background: #fff;
    border-top: 1px solid #e4e7ed;
    padding: 16px;

    .toolbar-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
    }

    .toolbar-content {
      :deep(.el-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .el-checkbox {
          margin-right: 0;

          .el-checkbox__label {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 100;

    .el-icon {
      font-size: 32px;
      color: #409eff;
      margin-bottom: 12px;
    }

    span {
      font-size: 14px;
      color: #606266;
    }
  }

  .connection-status {
    position: absolute;
    bottom: 16px;
    right: 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    z-index: 20;

    &.connected {
      background: #f0f9ff;
      color: #067f23;
      border: 1px solid #b3e5fc;

      .el-icon {
        color: #067f23;
      }
    }

    &.connecting {
      background: #fff7e6;
      color: #d46b08;
      border: 1px solid #ffd591;

      .el-icon {
        color: #d46b08;
      }
    }

    &.disconnected {
      background: #fff2f0;
      color: #cf1322;
      border: 1px solid #ffccc7;

      .el-icon {
        color: #cf1322;
      }
    }
  }
}

// 全屏模式样式
.fullscreen-mode {
  .map-display-area {
    border: none;
    border-radius: 0;
  }

  .point-toolbar {
    border-top: 1px solid #e4e7ed;
    background: #fafafa;
  }
}

// 动画
@keyframes fadeInOut {
  0%, 100% {
    opacity: 0;
  }
  10%, 90% {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .intelligent-robot-map {
    .point-toolbar {
      padding: 12px;

      .toolbar-content {
        :deep(.el-checkbox-group) {
          gap: 12px;

          .el-checkbox {
            .el-checkbox__label {
              font-size: 12px;

              .el-icon {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    .zoom-info {
      top: 12px;
      right: 12px;
      padding: 6px 10px;
      font-size: 12px;
    }

    .connection-status {
      bottom: 12px;
      right: 12px;
      padding: 4px 8px;
      font-size: 11px;
    }
  }
}
</style>
