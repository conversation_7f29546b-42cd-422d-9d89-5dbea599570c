<template>
	<div v-if="!item.hidden">
		<template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
			<app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)">
				<el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }" style="height: 48px;">
					<template #title>
						<a @click="redirectClick(item)"><span class="menu-title" :title="hasTitle(onlyOneChild.meta.title)">{{ onlyOneChild.meta.title }}</span></a>
					</template>
				</el-menu-item>
			</app-link>
		</template>

		<el-menu-item v-else ref="subMenu" :index="resolvePath(item.path)" teleported style="height: 48px;" class="subMenu">
			<template v-if="item.meta" #title>
				<a @click="menuClick(item.children)"><span class="menu-title" :title="hasTitle(item.meta.title)">{{ item.meta.title }}</span></a>
			</template>
		</el-menu-item>

	</div>
</template>

<script setup>
	import {
		isExternal
	} from '@/utils/validate'
	import AppLink from './Link'
	import {
		getNormalPath
	} from '@/utils/hyc'
	import useMenuStore from '@/store/modules/menu'
	import Cookies from 'js-cookie'
import { watch } from 'vue'

	const route = useRoute()
	
	const { proxy } = getCurrentInstance()

	const props = defineProps({
		// route object
		item: {
			type: Object,
			required: true
		},
		isNest: {
			type: Boolean,
			default: false
		},
		basePath: {
			type: String,
			default: ''
		}
	})

	const onlyOneChild = ref({})
	const menuStore = useMenuStore()		

	function menuClick(selectionMenu) {
		let routes = selectionMenu.filter(f => f.name == route.name)		
		if (selectionMenu.length > 0 && routes.length < 1) {			
			proxy.$router.push({
				name: selectionMenu[0].name
			})
		}
		menuStore.setBasePath(props.basePath)
		menuStore.setSelectionMenu(selectionMenu)
	}

	defineExpose({
		menuClick
	});

	function redirectClick(row) {
		if (row.redirect) {
			menuStore.setBasePath(null)
		}
		stopAllPlay()
		destroyPlugin()
	}

	// 销毁海康摄像机插件
	function destroyPlugin() {
		WebVideoCtrl.I_DestroyPlugin().then(() => {
			console.log('销毁成功！')
		}).catch(() => {
			console.log('销毁失败！')
		})
	}

	function stopAllPlay() {
		WebVideoCtrl.I_StopAllPlay().then(() => {
			console.log('关闭全部视频播放成功！')
		}).catch(() => {
			console.log('关闭全部视频播放失败！')
		})
	}

	function hasOneShowingChild(children = [], parent) {
		if (!children) {
			children = []
		}
		const showingChildren = children.filter(item => {
			if (item.hidden) {
				return false
			}
			onlyOneChild.value = item
			return true
		})

		// When there is only one child router, the child router is displayed by default
		if (showingChildren.length === 1) {
			return true
		}

		// Show parent if there are no child router to display
		if (showingChildren.length === 0) {
			onlyOneChild.value = {
				...parent,
				path: '',
				noShowingChildren: true
			}
			return true
		}

		return false
	}

	function resolvePath(routePath, routeQuery) {
		if (isExternal(routePath)) {
			return routePath
		}
		if (isExternal(props.basePath)) {
			return props.basePath
		}
		if (routeQuery) {
			let query = JSON.parse(routeQuery)
			return {
				path: getNormalPath(props.basePath + '/' + routePath),
				query: query
			}
		}
		return getNormalPath(props.basePath + '/' + routePath)
	}

	function hasTitle(title) {
		if (title.length > 5) {
			return title
		} else {
			return ""
		}
	}
</script>

<style scoped>
	
</style>