﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="23px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="691px" y="425px" width="24px" height="23px" filterUnits="userSpaceOnUse" id="filter346">
      <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.698039215686274 0  " in="shadowComposite" />
    </filter>
    <g id="widget347">
      <path d="M 702 429  C 705.92 429  709 431.86  709 435.5  C 709 439.14  705.92 442  702 442  C 698.08 442  695 439.14  695 435.5  C 695 431.86  698.08 429  702 429  Z " fill-rule="nonzero" fill="#333333" stroke="none" />
      <path d="M 702 429.5  C 705.64 429.5  708.5 432.14  708.5 435.5  C 708.5 438.86  705.64 441.5  702 441.5  C 698.36 441.5  695.5 438.86  695.5 435.5  C 695.5 432.14  698.36 429.5  702 429.5  Z " stroke-width="1" stroke="#ffffff" fill="none" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -691 -425 )">
    <use xlink:href="#widget347" filter="url(#filter346)" />
    <use xlink:href="#widget347" />
  </g>
</svg>