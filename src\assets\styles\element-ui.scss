// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link{
  color: var(--el-color-primary) !important;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  padding: 0;
  top: 90px;
}

.el-dialog__header {
	padding: 20px;
	border-bottom: 1px solid #ececec;
	height: 54px;
	display: flex;
	align-items: center
}

.el-dialog__title {
	font-size: 20px;
	color: #000000;	
}

.el-dialog__body {
	padding: 20px;	
	box-shadow: 0 1px 3px rgba(84, 99, 114, 0.5);
}

.el-dialog__footer {
	padding: 20px;
	height: 68px;
	border-top: 1px solid #ececec;
}

.el-dialog__headerbtn {
	background: url('../images/dialog/a0301.png') no-repeat;
	margin-top: 15px;
	height: 30px;
}

.el-dialog__headerbtn:hover {
	background: url('../images/dialog/a0302.png') no-repeat;
	margin-top: 15px;
}

.el-dialog__close {
	display: none;
}