# 智能机器人地图组件

## 概述

本组件实现了智能机器人地图显示功能，包括地图显示、WebSocket实时通信、机器人位置跟踪、障碍物显示、AI报警等功能。

## 功能特性

### 核心功能
1. **地图显示** - 显示SVG格式的地图
2. **WebSocket连接** - 与后端建立实时通信
3. **机器人跟踪** - 实时显示机器人位置和状态
4. **障碍物显示** - 显示地图上的障碍点
5. **AI报警** - 显示AI检测到的异常情况
6. **点位管理** - 可选择性显示不同类型的点位

### 交互功能
1. **地图缩放** - 鼠标滚轮缩放地图
2. **地图拖拽** - 鼠标左键拖拽移动地图
3. **全屏模式** - F11进入全屏，ESC退出全屏
4. **悬浮提示** - 鼠标悬停显示详细信息

## 组件结构

```
src/views/intelligentRobot/intelligentRobotMap/
├── index.vue                                    # 主页面
├── components/
│   └── IntelligentRobotMapComponent.vue        # 地图组件
└── README.md                                   # 本文档
```

## 使用方法

### 1. 在页面中引入组件

```vue
<template>
  <div>
    <intelligent-robot-map-component
      ref="mapComponentRef"
      @map-loaded="handleMapLoaded"
      @websocket-connected="handleWebSocketConnected"
      @websocket-disconnected="handleWebSocketDisconnected"
    />
  </div>
</template>

<script setup>
import IntelligentRobotMapComponent from './components/IntelligentRobotMapComponent.vue'

const mapComponentRef = ref(null)

const handleMapLoaded = (mapData) => {
  console.log('地图加载完成:', mapData)
}

const handleWebSocketConnected = () => {
  console.log('WebSocket连接成功')
}

const handleWebSocketDisconnected = () => {
  console.log('WebSocket连接断开')
}
</script>
```

### 2. 组件事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| map-loaded | 地图加载完成 | mapData: 地图数据对象 |
| websocket-connected | WebSocket连接成功 | - |
| websocket-disconnected | WebSocket连接断开 | - |

### 3. 组件方法

通过ref可以调用组件的方法：

```javascript
// 重新加载地图数据
mapComponentRef.value.loadMapData()

// 切换全屏模式
mapComponentRef.value.toggleFullscreen()

// 更新地图元素
mapComponentRef.value.updateMapElements()
```

## WebSocket通信

### 连接配置
- **URL**: `ws://${location.host}/wsDispatch`
- **认证**: Bearer Token (从localStorage获取)

### 消息类型

#### 1. 地图信息 (MAP)
```json
{
  "type": "MAP",
  "data": {
    "o": [{"x": 1.0, "y": 2.0, "type": 0}],  // 障碍点
    "ps": [["1", 1, 23.4, 56.3, 0.0, 0, "LandMark", "标记点A"]],  // 点位
    "ai": [{"name": "漏液报警", "x": 1.0, "y": 2.0, "type": "leak"}]  // AI报警
  }
}
```

#### 2. 机器人状态 (ROBOT)
```json
{
  "type": "ROBOT",
  "data": [{
    "id": 1,
    "name": "机器人1",
    "pos": {"x": 1.0, "y": 2.0},
    "theta": 1.57,
    "clr": "#ff0000",
    "s": "运行中",
    "bty": "85%",
    "vel": 1.2
  }]
}
```

#### 3. 障碍变更 (OBSTACLE)
```json
{
  "type": "OBSTACLE",
  "data": [
    {"x": 1.0, "y": 2.0, "xmapId": "1", "robotId": 1, "type": 0}
  ]  // 当前存在的障碍列表，空数组[]表示所有障碍已清除
}
```

**重要说明**:
- 当`data`为空数组`[]`时，表示所有障碍都已消除，地图会清空所有障碍显示
- 当`data`有内容时，会用新的障碍列表完全替换原有障碍
- 障碍的增加、减少、清空都会实时反映在地图上

#### 4. AI报警变更 (AI_ALARM)
```json
{
  "type": "AI_ALARM",
  "data": [
    {"name": "漏液报警", "x": 1.0, "y": 2.0, "type": "leak", "time": "2025-01-06 10:30:00", "dept": "ETCH"}
  ]  // 当前存在的AI报警列表，空数组[]表示所有AI报警已清除
}
```

**重要说明**:
- 当`data`为空数组`[]`时，表示所有AI报警都已消除，地图会清空所有AI报警显示
- 当`data`有内容时，会用新的AI报警列表完全替换原有报警
- AI报警的增加、减少、清空都会实时反映在地图上

## 点位类型

支持以下点位类型的显示控制：

| 类型 | 标识 | 图标 | 说明 |
|------|------|------|------|
| 标记点 | LandMark | 📍 | 普通标记点 |
| 充电点 | ChargePoint | 🔋 | 机器人充电位置 |
| 识别点 | IdenPoint | 👁️ | AI识别检测点 |
| 红外测温 | InfraredDetection | 🌡️ | 温度检测点 |
| 十字路口 | Crossroads | ➕ | 路径交叉点 |
| 出口 | Exit | 🚪 | 出入口位置 |
| 停车位 | Parking | 🅿️ | 停车区域 |

## AI报警类型

| 类型 | 标识 | 颜色 | 说明 |
|------|------|------|------|
| 漏液报警 | leak | #ff6b6b | 检测到液体泄漏 |
| 异物检测 | matter | #4ecdc4 | 检测到异常物体 |
| 温度报警 | temp | #45b7d1 | 温度异常 |
| 烟雾报警 | gas | #96ceb4 | 检测到烟雾或气体 |

## 坐标转换

组件使用自定义的坐标转换逻辑，基于mapData中的参数：

### mapData结构
```json
{
  "cadUrl": "/profile/upload/???/?.svg",
  "scale": 0.0037,           // 米到pt的转换比例
  "offsetX": 0,              // X轴偏移量
  "offsetY": 0,              // Y轴偏移量
  "minPosX": 0,              // 地图X轴最小值
  "maxPosX": 100,            // 地图X轴最大值
  "minPosY": 0,              // 地图Y轴最小值
  "maxPosY": 100,            // 地图Y轴最大值
  "cadWidth": 26.89,         // CAD图纸宽度（米）
  "cadHeight": 26.89         // CAD图纸高度（米）
}
```

### 转换算法
```javascript
// X轴坐标转换
ptX = (x - minPosX) * (1/scale) + offsetX

// Y轴坐标转换
ptY = (y - minPosY) * (1/scale) + offsetY
```

### 转换过程
1. **输入坐标**: 机器人实际位置坐标（米为单位）
2. **减去最小值**: 将坐标转换为相对坐标
3. **应用比例**: 使用scale参数转换为pt单位
4. **应用偏移**: 加上offsetX/offsetY偏移量
5. **输出坐标**: ECharts可用的屏幕坐标

## 样式定制

组件使用SCSS编写样式，支持以下定制：

### 主要CSS类
- `.intelligent-robot-map` - 主容器
- `.map-display-area` - 地图显示区域
- `.point-toolbar` - 点位选择工具栏
- `.connection-status` - 连接状态指示器

### 响应式设计
组件支持移动端适配，在768px以下会调整布局和字体大小。

## 依赖项

- Vue 3.x
- Element Plus
- ECharts 5.x
- SCSS

## 注意事项

1. **WebSocket连接**: 确保后端WebSocket服务正常运行
2. **地图数据**: 需要后端提供SVG格式的地图数据
3. **坐标系统**: 确保前后端坐标系统一致
4. **性能优化**: 大量数据时建议启用虚拟化或分页
5. **浏览器兼容**: 需要支持WebSocket的现代浏览器

## 故障排除

### 常见问题

1. **地图不显示**
   - 检查API接口是否正常
   - 确认SVG数据格式正确

2. **WebSocket连接失败**
   - 检查网络连接
   - 确认Token有效性
   - 检查后端WebSocket服务

3. **坐标显示错误**
   - 检查坐标转换参数
   - 确认地图比例尺设置

4. **性能问题**
   - 减少同时显示的元素数量
   - 优化更新频率
   - 检查内存泄漏

## 更新日志

### v1.1.1 (2025-01-06)
- 🧹 **修复障碍清空逻辑**: 当obstacles为空数组[]时正确清空地图上的所有障碍显示
- 🚨 **修复AI报警清空逻辑**: 当aiAlarms为空数组[]时正确清空地图上的所有AI报警显示
- 🔄 **优化更新机制**: 障碍和AI报警的增加、减少、清空都能实时反映在地图上
- 🧪 **增强测试功能**: 在测试页面添加清空障碍和AI报警的测试按钮
- 📝 **完善文档说明**: 详细说明空数组的处理逻辑和更新机制

### v1.1.0 (2025-01-06)
- 🔧 **重构坐标转换系统**: 基于mapData参数实现自定义坐标转换
- 🗺️ **改进地图加载**: 支持从URL加载SVG内容，而非直接嵌入
- 📊 **优化ECharts配置**: 根据地图实际尺寸动态设置坐标系
- 🎯 **增强图标系统**: 为不同类型的点位和元素使用不同的图标
- 🔄 **自动重新初始化**: 地图加载完成后自动重新配置ECharts坐标系
- 🐛 **修复坐标问题**: 解决Y轴反转和坐标偏移问题

### v1.0.0 (2025-01-05)
- 初始版本发布
- 实现基础地图显示功能
- 支持WebSocket实时通信
- 添加机器人位置跟踪
- 实现障碍物和AI报警显示
- 支持点位类型选择
- 添加地图交互功能（缩放、拖拽、全屏）
