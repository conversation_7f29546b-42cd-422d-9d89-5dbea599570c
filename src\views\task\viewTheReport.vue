<template>
	<div class="app-container">
		<el-card v-loading="loading" style="margin: 28px;">
			<template #header>
			  <div style="height: 64px; font-size: 18px; font-weight: bold; padding-top: 15px;">
				<span class="blue-line">任务详情</span>
			  </div>
			</template>
			<div style="padding: 10px 20px 10px 20px; height: 80px; border-bottom: 1px #eeeeee solid;">
				<el-row>
					<el-col :span="8">
						<div style="display: grid;">
							<span style="margin-bottom: 10px; font-size: 13px; color: #999999;">任务名称</span>
							<span style="color: #1474FE;font-size: 14px;">{{data.taskName}}</span>
						</div>
					</el-col>
					<el-col :span="8">
						<div style="display: grid;">
							<span style="margin-bottom: 10px; font-size: 13px; color: #999999;">任务状态</span>
							<span style="font-size: 14px;"><dict-tag :options="task_instance_status" :value="data.status" /></span>
						</div>
					</el-col>
					<el-col :span="8">
						<div style="display: grid;">
							<span style="margin-bottom: 10px; font-size: 13px; color: #999999;">所属部门</span>
							<span style="font-size: 14px;">{{data.deptName}}</span>
						</div>
					</el-col>
				</el-row>
			</div>
			<div style="padding: 10px 20px 10px 20px; height: 80px;">
				<el-row>
					<el-col :span="8">
						<div style="display: grid;">
							<span style="margin-bottom: 10px; font-size: 13px; color: #999999;">开始时间</span>
							<span style="font-size: 14px;">{{parseTime(data.startTime)}}</span>
						</div>
					</el-col>
					<el-col :span="8">
						<div style="display: grid;">
							<span style="margin-bottom: 10px; font-size: 13px; color: #999999;">结束时间</span>
							<span style="font-size: 14px;">{{parseTime(data.finishTime)}}</span>
						</div>
					</el-col>
					<el-col :span="8">
						<div style="display: grid;">
							<span style="margin-bottom: 10px; font-size: 13px; color: #999999;">环境信息</span>
							<span style="font-size: 14px;">{{data.envReport}}</span>
						</div>
					</el-col>
				</el-row>
			</div>
			
			<div style="height: 40px; padding: 10px 20px; background-color: #F8F9FC;">
				<span style="font-size: 14px; font-weight: bold;">巡检结果</span>
			</div>
			
			<div style="margin: 16px 0;">
				<el-row :gutter="20">
					<el-col :span="4">
						<div style="border: 1px #edeef0 solid; display: flex; justify-content: space-evenly; align-items: center; height: 64px;">
							<span style="font-size: 13px; color: #999999;">已巡检</span>
							<span style="font-size: 24px; font-weight: bold;">{{data.pointHealthNum + data.pointAlarmNum + data.abnormalNum}}</span>
						</div>
					</el-col>
					<el-col :span="4">
						<div style="border: 1px #edeef0 solid; display: flex; justify-content: space-evenly; align-items: center; height: 64px;">
							<span style="font-size: 13px; color: #999999;">正常点位</span>
							<span style="font-size: 24px; font-weight: bold;">{{data.pointHealthNum}}</span>
						</div>
					</el-col>
					<el-col :span="4">
						<div style="border: 1px #edeef0 solid; display: flex; justify-content: space-evenly; align-items: center; height: 64px;">
							<span style="font-size: 13px; color: #999999;">告警点位</span>
							<span style="font-size: 24px; font-weight: bold;">{{data.pointAlarmNum}}</span>
						</div>
					</el-col>
					<el-col :span="4">
						<div style="border: 1px #edeef0 solid; display: flex; justify-content: space-evenly; align-items: center; height: 64px;">
							<span style="font-size: 13px; color: #999999;">不可达</span>
							<span style="font-size: 24px; font-weight: bold;">{{data.unreachableNum}}</span>
						</div>
					</el-col>
					<el-col :span="4">
						<div style="border: 1px #edeef0 solid; display: flex; justify-content: space-evenly; align-items: center; height: 64px;">
							<span style="font-size: 13px; color: #999999;">未识别</span>
							<span style="font-size: 24px; font-weight: bold;">{{data.abnormalNum}}</span>
						</div>
					</el-col>
					<el-col :span="4">
						<div style="border: 1px #edeef0 solid; display: flex; justify-content: space-evenly; align-items: center; height: 64px;">
							<span style="font-size: 13px; color: #999999;">智能报警</span>
							<span style="font-size: 24px; font-weight: bold;">{{data.aiAlarmNum}}</span>
						</div>
					</el-col>
				</el-row>
			</div>
			
			<div style="height: 40px; padding: 10px 20px; background-color: #F8F9FC;">
				<span style="font-size: 14px; font-weight: bold;">智能报警</span>
			</div>
			
			<el-table :data="data.alarmAiList">
				<el-table-column type="index" label="序号" align="center" width="60" />
				<el-table-column label="报警名称" align="center" prop="alarmName" />
				<el-table-column label="识别类型" align="center" prop="idenType" />
				<el-table-column label="识别结果" align="center" prop="idenResult" />
				<el-table-column label="报警等级" align="center" prop="alarmLevel" />
				<el-table-column label="报警时间" align="center">
					<template #default="scope">
						{{ parseTime(scope.row.triggerTime) }}
					</template>
				</el-table-column>
				<el-table-column label="备注" align="center" prop="remark" />
				<el-table-column label="现场照片" align="center">
					<template #default="scope">
						<el-image preview-teleported style="width: 90px; height: 90px" :src="apiUrl + scope.row.imgUrl" :zoom-rate="1.2"
							:preview-src-list="[apiUrl + scope.row.imgUrl]" :max-scale="7" :min-scale="0.2" fit="cover"
							show-progress :initial-index="0" />
					</template>
				</el-table-column>
			</el-table>
			
			<!-- 0未抵达 | 1执行中 | 3巡检正常 | 7识别异常 | 8不可达 | 9告警 -->
			<div style="height: 40px; padding: 10px 20px; background-color: #F8F9FC; margin-top: 20px;">
				<span style="font-size: 14px; font-weight: bold;">告警点位</span>
			</div>
			
			<el-table :data="data.status9">
				<el-table-column type="index" label="序号" align="center" width="60" />
				<el-table-column label="点位模型" align="center" prop="idenModelName" />
				<el-table-column label="点位名称" align="center" prop="pointName" />
				<el-table-column label="识别结果" align="center">
					<template #default="scope">
						<div v-for="item in scope.row.taskInstanceNodeResultList ">
							<span>{{item.idenModelParamName}} = {{ item.floatValue || item.enumValue }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="报警等级" align="center">
					<template #default="scope">
						<span>{{scope.row.ruleResult + scope.row.ruleExpressionText}}</span>
					</template>
				</el-table-column>
				<el-table-column label="采集时间" align="center">
					<template #default="scope">
						{{ parseTime(scope.row.idenTime) }}
					</template>
				</el-table-column>
				<el-table-column label="现场照片" align="center">
					<template #default="scope">
						<el-image preview-teleported style="width: 90px; height: 90px" :src="apiUrl + scope.row.imgUrl" :zoom-rate="1.2"
							:preview-src-list="[apiUrl + scope.row.imgUrl]" :max-scale="7" :min-scale="0.2" fit="cover"
							show-progress :initial-index="0" />
					</template>
				</el-table-column>
			</el-table>
			
			<div style="height: 40px; padding: 10px 20px; background-color: #F8F9FC; margin-top: 20px;">
				<span style="font-size: 14px; font-weight: bold;">异常识别</span>
			</div>
			
			<el-table :data="data.status7">
				<el-table-column type="index" label="序号" align="center" width="60" />
				<el-table-column label="点位模型" align="center" prop="idenModelName" />
				<el-table-column label="点位名称" align="center" prop="pointName" />
				<el-table-column label="状态" align="center">
					<template #default="scope">
						<span>{{scope.row.ruleResult + scope.row.ruleExpressionText}}</span>
					</template>
				</el-table-column>
				<el-table-column label="采集时间" align="center">
					<template #default="scope">
						{{ parseTime(scope.row.idenTime) }}
					</template>
				</el-table-column>
				<el-table-column label="识别结果" align="center">
					<template #default="scope">
						<div v-for="item in scope.row.taskInstanceNodeResultList ">
							<span>{{item.idenModelParamName}} = {{ item.floatValue || enumValue }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="现场照片" align="center">
					<template #default="scope">
						<el-image preview-teleported style="width: 90px; height: 90px" :src="apiUrl + scope.row.imgUrl" :zoom-rate="1.2"
							:preview-src-list="[apiUrl + scope.row.imgUrl]" :max-scale="7" :min-scale="0.2" fit="cover"
							show-progress :initial-index="0" />
					</template>
				</el-table-column>
			</el-table>
			
			<div style="height: 40px; padding: 10px 20px; background-color: #F8F9FC; margin-top: 20px;">
				<span style="font-size: 14px; font-weight: bold;">正常点位</span>
			</div>
			
			<el-table :data="data.status3">
				<el-table-column type="index" label="序号" align="center" width="60" />
				<el-table-column label="点位模型" align="center" prop="idenModelName" />
				<el-table-column label="点位名称" align="center" prop="pointName" />
				<el-table-column label="识别结果" align="center">
					<template #default="scope">
						<div v-for="item in scope.row.taskInstanceNodeResultList ">
							<span>{{item.idenModelParamName}} = {{ item.floatValue || enumValue }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="状态" align="center">
					<template #default="scope">
						<span>{{scope.row.ruleResult + scope.row.ruleExpressionText}}</span>
					</template>
				</el-table-column>
				<el-table-column label="采集时间" align="center">
					<template #default="scope">
						{{ parseTime(scope.row.idenTime) }}
					</template>
				</el-table-column>
			
				<el-table-column label="现场照片" align="center">
					<template #default="scope">
						<el-image preview-teleported style="width: 90px; height: 90px" :src="apiUrl + scope.row.imgUrl" :zoom-rate="1.2"
							:preview-src-list="[apiUrl + scope.row.imgUrl]" :max-scale="7" :min-scale="0.2" fit="cover"
							show-progress :initial-index="0" />
					</template>
				</el-table-column>
			</el-table>
		</el-card>
		
	</div>
</template>

<script>
	import taskInstance from '@/api/task/taskInstance';

	export default {
		data() {
			return {
				loading: false,
				taskId: this.$route.query.id,
				data: {},
				apiUrl: import.meta.env.VITE_APP_BASE_API,
				task_instance_status: getCurrentInstance().proxy.useDict("task_instance_status").task_instance_status,
			}
		},
		created() {
			this.getList()
		},
		methods: {
			getList() {
				this.loading = true
				taskInstance.getViewTheReport(this.taskId).then(res => {
					let status3 = res.data.taskInstanceNodeList.filter(f=>f.status == 3)
					let status7 = res.data.taskInstanceNodeList.filter(f=>f.status == 7)
					let status9 = res.data.taskInstanceNodeList.filter(f=>f.status == 9)
					this.data = res.data
					this.data.status3 = status3
					this.data.status7 = status7
					this.data.status9 = status9
					this.loading = false
				})
			}
		}
	}
</script>

<style scoped>
	.el-descriptions {
		margin-top: 20px;
	}

	/deep/.el-descriptions__header {
		justify-content: center;
	}
	
	.blue-line {
	  border-left: 3px solid #409EFF;
	  padding-left: 10px;
	}
	
	
	/deep/.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
		background-color: #F8F9FC !important;
		color: #666666;
		font-weight: unset;
		font-size: 14px;
		border: 0;
	}
	
	/deep/.el-table td.el-table__cell div {
		font-size: 14px;
		color: #000;
	}
	
	/deep/.el-table--enable-row-transition .el-table__body td.el-table__cell {
		border-left: 1px solid #ebeef5;
	}
	
	/deep/.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
		border-bottom: 0;
		border-top: 1px solid #ebeef5;
	}
</style>