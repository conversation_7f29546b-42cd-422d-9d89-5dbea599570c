<template>
	<div class="app">
		<div class="left_div">
			<div class="left_div_1">
				<span style="color: #fff; font-weight: bold; font-size: 20px; position: absolute; top: 140px; left: 150px;">机器人总览</span>
				<div style="color: #fff; position: absolute; top: 200px; left: 90px; display: flex; align-items:center;">
					<span>机器人数量</span>
					<img src="../assets/images/home/<USER>" alt="" style="margin: 0 10px 0 10px; width: 86px; height: 1px;"/>
					<span style="font-weight: bold; font-size: 20px;">24</span>
				</div>
				<div style="color: #fff; font-weight: bold; position: absolute; top: 250px; left: 74px;">
					<img src="../assets/images/home/<USER>" alt="" style="width: 76px; margin-right: 10px;"/>
					<img src="../assets/images/home/<USER>" alt="" style="width: 76px; margin-right: 10px;"/>
					<img src="../assets/images/home/<USER>" alt="" style="width: 76px;"/>
					<div style="display: grid; text-align: center; position: absolute; top: 15px; left: 24px;">
						<span style="font-size: 14px;">正常</span>
						<span style="margin-top: 5px; font-size: 20px;">8</span>
					</div>
					<div style="display: grid; text-align: center; position: absolute; top: 15px; left: 110px;">
						<span style="font-size: 14px;">故障</span>
						<span style="margin-top: 5px; font-size: 20px;">8</span>
					</div>
					<div style="display: grid; text-align: center; position: absolute; top: 15px; left: 190px;">
						<span style="font-size: 14px;">充电中</span>
						<span style="margin-top: 5px; font-size: 20px;">8</span>
					</div>
				</div>
			</div>
			<div class="left_div_2">
				<span style="color: #fff; font-weight: bold; font-size: 20px; position: absolute; top: 400px; left: 145px;">任务总结</span>
				<div style="color: #fff; position: absolute; top: 460px; left: 90px; display: flex; align-items:center;">
					<span>正在执行任务数量</span>
					<img src="../assets/images/home/<USER>" alt="" style="margin: 0 10px 0 10px; width: 45px;"/>
					<span style="font-weight: bold; font-size: 20px;">24</span>
				</div>
				<div style="color: #fff; font-weight: bold; position: absolute; top: 530px; left: 120px; display: flex;">
					<div style="display: grid; text-align: center; margin-right: 50px;">
						<span style="font-size: 14px; color: #496BBA;">巡检任务</span>
						<span style="margin-top: 5px; font-size: 20px;">8</span>
					</div>
					<div style="display: grid; text-align: center;">
						<span style="font-size: 14px; color: #496BBA;">陪同任务</span>
						<span style="margin-top: 5px; font-size: 20px;">8</span>
					</div>
				</div>
			</div>
			<div class="left_div_3">
				<span style="color: #fff; font-weight: bold; font-size: 20px; position: absolute; top: 665px; left: 145px;">报警总结</span>
				<div style="color: #fff;">
					<div style="position: relative; top: 91px; left: 40px;">
						<span style="font-size: 14px; color: #496BBA; margin-right: 10px;">智能报警</span>
						<span style="margin-top: 5px; font-size: 20px;">81</span>
					</div>
					<div style="position: relative; top: 66px; left: 178px;">
						<span style="font-size: 14px; color: #496BBA; margin-right: 10px;">实时报警</span>
						<span style="margin-top: 5px; font-size: 20px;">81</span>
					</div>
					<div style="position: relative; top: 112px; left: 40px;">
						<span style="font-size: 14px; color: #496BBA; margin-right: 10px;">硬件报警</span>
						<span style="margin-top: 5px; font-size: 20px;">81</span>
					</div>
					<div style="position: relative; top: 87px; left: 178px;">
						<span style="font-size: 14px; color: #496BBA; margin-right: 10px;">陪同报警</span>
						<span style="margin-top: 5px; font-size: 20px;">81</span>
					</div>
				</div>
				
			</div>
		</div>
		<div class="left2_div">
			<img src="../assets/images/home/<USER>" alt="" class="left2_div_img"/>
			<div style="display: grid;">
				<span style="color: #fff; font-weight: bold; font-size: 20px; margin-bottom: 10px;">智能报警</span>
				<img src="../assets/images/home/<USER>" alt=""/>	
			</div>
			<div style="display: grid; position: absolute; top: 614px; right: 681px;">
				<span style="color: #fff; font-weight: bold; font-size: 20px; margin-bottom: 10px;">实时报警</span>
				<img src="../assets/images/home/<USER>" alt="" class="left2_div2_table_img"/>
			</div>
			<div style="display: flex;">
				<div class="left2_div_table" style="height: 230px; overflow-y: auto;">					
					<table class="left2_div_table_1">
						<thead>
							<tr style="height: 52px; font-size: 14px; color: #405070; background-color: #07080c;">
								<th>序号</th>
								<th>报警名称</th>
								<th>识别类型</th>
								<th>报警时间</th>
								<th>状态</th>
							</tr>
						</thead>
						<tbody>
							<tr style="height: 38px; background-color: #0f131c;" v-for="(item, index) in smartAlarm">
								<td align="center">{{ index + 1 }}</td>
								<td align="center">222</td>
								<td align="center">333</td>
								<td align="center">444</td>
								<td align="center">555</td>
							</tr>
						</tbody>
						
					</table>
					
				</div>
				<div class="left2_div2_table" style="height: 230px; overflow-y: auto;">					
					<table class="left2_div_table_2">
						<thead>
							<tr style="height: 52px; font-size: 14px; color: #405070; background-color: #07080c;">
								<th>序号</th>
								<th>报警名称</th>
								<th>识别类型</th>
								<th>报警时间</th>
								<th>状态</th>
							</tr>
						</thead>
						<tbody>
							<tr style="height: 38px; background-color: #0f131c;" v-for="(item, index) in smartAlarm">
								<td align="center">{{ index + 1 }}</td>
								<td align="center">222</td>
								<td align="center">333</td>
								<td align="center">444</td>
								<td align="center">555</td>
							</tr>
						</tbody>
						
					</table>
				</div>
			</div>
			
		</div>
		<div class="right_div">
			<div class="right_div_1">
				<span style="font-size: 20px; font-weight: bold; position: relative; top: 15px; left: 40px;">周报警数统计</span>
				<div id="chart1" style="height: 250px;"></div>
			</div>
			<div class="right_div_2">
				<span style="font-size: 20px; font-weight: bold; position: relative; top: 15px; left: 40px;">周巡检点位数统计</span>
				<div id="chart2" style="height: 250px;"></div>
			</div>
		</div>
	</div>
</template>

<script>
	import * as echarts from 'echarts';
	
	const leftShape = echarts.graphic.extendShape({
	  buildPath(ctx, shape) {
	    const {
	      topBasicsYAxis,
	      bottomYAxis,
	      basicsXAxis
	    } = shape;
	    // 侧面宽度
	    const WIDTH = 20;
	    // 斜角高度
	    const OBLIQUE_ANGLE_HEIGHT = 3.6;
	
	    const p1 = [basicsXAxis - WIDTH, topBasicsYAxis - OBLIQUE_ANGLE_HEIGHT];
	    const p2 = [basicsXAxis - WIDTH, bottomYAxis];
	    const p3 = [basicsXAxis, bottomYAxis];
	    const p4 = [basicsXAxis, topBasicsYAxis];
	
	    ctx.moveTo(p1[0], p1[1]);
	    ctx.lineTo(p2[0], p2[1]);
	    ctx.lineTo(p3[0], p3[1]);
	    ctx.lineTo(p4[0], p4[1]);
	  },
	});
	
	const rightShape = echarts.graphic.extendShape({
	  buildPath(ctx, shape) {
	    const {
	      topBasicsYAxis,
	      bottomYAxis,
	      basicsXAxis
	    } = shape;
	    // 侧面宽度
	    const WIDTH = 20;
	    // 斜角高度
	    const OBLIQUE_ANGLE_HEIGHT = -3.6;
	
	    const p1 = [basicsXAxis, topBasicsYAxis];
	    const p2 = [basicsXAxis, bottomYAxis];
	    const p3 = [basicsXAxis + WIDTH, bottomYAxis];
	    const p4 = [basicsXAxis + WIDTH, topBasicsYAxis + OBLIQUE_ANGLE_HEIGHT];
	
	    ctx.moveTo(p1[0], p1[1]);
	    ctx.lineTo(p2[0], p2[1]);
	    ctx.lineTo(p3[0], p3[1]);
	    ctx.lineTo(p4[0], p4[1]);
	  },
	});
	
	const topShape = echarts.graphic.extendShape({
	  buildPath(ctx, shape) {
	    const {
	      topBasicsYAxis,
	      basicsXAxis
	    } = shape;
	    // 侧面宽度
	    const WIDTH = 20;
	    // 斜角高度
	    const OBLIQUE_ANGLE_HEIGHT = 3.6;
	
	    const p1 = [basicsXAxis, topBasicsYAxis];
	    const p2 = [basicsXAxis + WIDTH, topBasicsYAxis - OBLIQUE_ANGLE_HEIGHT];
	    const p3 = [basicsXAxis, topBasicsYAxis - OBLIQUE_ANGLE_HEIGHT * 2];
	    const p4 = [basicsXAxis - WIDTH, topBasicsYAxis - OBLIQUE_ANGLE_HEIGHT];
	
	    ctx.moveTo(p1[0], p1[1]);
	    ctx.lineTo(p2[0], p2[1]);
	    ctx.lineTo(p3[0], p3[1]);
	    ctx.lineTo(p4[0], p4[1]);
	  },
	});
	
	// 注册图形元素
	echarts.graphic.registerShape('leftShape', leftShape);
	echarts.graphic.registerShape('rightShape', rightShape);
	echarts.graphic.registerShape('topShape', topShape);
	
	export default {
	  data() {
	    return {
	      smartAlarm: [{},{},{},{},{},{},{},{},{},{},{},{}],
		  realtimeAlarm: [{},{},{},{},{},{},{},{},{},{},{},{}]
	    }
	  },
	  created() {
	  	
	  },
	  mounted() {
	  	this.chartInit()
	  },
	  methods: {
	    chartInit() {
			if (echarts.getInstanceByDom(document.getElementById('chart1'))) {
				echarts.dispose(document.getElementById('chart1'));
			}
			if (echarts.getInstanceByDom(document.getElementById('chart2'))) {
				echarts.dispose(document.getElementById('chart2'));
			}
			// 图表1
			let chart1 = document.getElementById('chart1');
			let myChart1 = echarts.init(chart1);
			let option1 = {
				backgroundColor: 'transparent',
				tooltip: {
				  trigger: 'axis',
				  axisPointer: {
				    type: 'shadow'
				  }
				},
				xAxis: {
				  type: 'category',
				  data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
				  axisLabel: {
				    color: '#fff',
				    formatter: function(params, index) {
				      return params.length > 7 ? params.substring(0, 7) + '...' : params;
				    }
				  },
				  axisLine: {
				    show: true,
				    lineStyle: {
				      type: 'solid',
				      width: 2,
				      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
				          offset: 0,
				          color: 'rgba(0,153,221,0)'
				        },
				        {
				          offset: 0.5,
				          color: 'rgba(118,250,255,1)'
				        },
				        {
				          offset: 1,
				          color: 'rgba(0,153,221,0)'
				        },
				      ])
				    }
				  }
				},				
				yAxis: {
				  type: 'value',
				  axisLabel: {
				    color: '#fff'
				  },
				  splitLine: {
				    show: true,
				    lineStyle: {
				      color: 'rgba(27,66,107, 1)'
				    }
				  }
				},
				series: [
					{
						name: '报警数',
						data: [120, 200, 150, 80, 70, 110, 130],
						type: 'custom',
						renderItem: (params, api) => {
							return this.getRenderItem(params, api);
						}
					}
				]
			};
			myChart1.setOption(option1);
			
			// 图表2
			let chart2 = document.getElementById('chart2');
			let myChart2 = echarts.init(chart2);
			let option2 = {
				backgroundColor: 'transparent',
				tooltip: {
				  trigger: 'axis',
				},
				xAxis: {
				  type: 'category',
				  data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
				  axisLabel: {
				    color: '#fff',
				    formatter: function(params, index) {
				      return params.length > 7 ? params.substring(0, 7) + '...' : params;
				    }
				  },
				  axisLine: {
				    show: true,
				    lineStyle: {
				      type: 'solid',
				      width: 2,
				      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
				          offset: 0,
				          color: 'rgba(0,153,221,0)'
				        },
				        {
				          offset: 0.5,
				          color: 'rgba(118,250,255,1)'
				        },
				        {
				          offset: 1,
				          color: 'rgba(0,153,221,0)'
				        },
				      ])
				    }
				  }
				},				
				yAxis: {
				  type: 'value',
				  axisLabel: {
				    color: '#fff'
				  },
				  splitLine: {
				    show: true,
				    lineStyle: {
				      color: 'rgba(27,66,107, 1)'
				    }
				  }
				},
				series: [
					{
						name: '巡检点位数',
					  data: [820, 932, 901, 934, 1290, 1330, 1320],
					  type: 'line',
					  smooth: true
					}
				]
			};
			myChart2.setOption(option2);
		},
		getRenderItem(params, api) {
		  // 柱子索引值
		  const {
		    seriesIndex
		  } = params;
		
		  // 基础坐标
		  const basicsCoord = api.coord([api.value(seriesIndex), api.value(1)]);
		  // 顶部基础 y 轴
		  const topBasicsYAxis = basicsCoord[1];
		  // 基础 x 轴
		  const basicsXAxis = basicsCoord[0];
		  // 底部 y 轴
		  const bottomYAxis = api.coord([api.value(seriesIndex), 0])[1];
		
		  return {
		    type: "group",
		    children: [{
		        type: "leftShape",
		        shape: {
		          topBasicsYAxis,
		          basicsXAxis,
		          bottomYAxis,
		        },
		        style: {
		          ...api.style(),
		          fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
		              offset: 0,
		              color: "rgba(65,114,176,0.8) "
		            },
		            {
		              offset: 1,
		              color: "rgba(58,101,156,0.5)"
		            },
		          ]),
		          text: '',
		          shadowBlur: 4,
		          shadowColor: 'rgba(58,101,156,0.3)',
		          shadowOffsetY: -25
		        },
		      },
		      {
		        type: "rightShape",
		        shape: {
		          topBasicsYAxis,
		          basicsXAxis,
		          bottomYAxis,
		        },
		        style: {
		          ...api.style(),
		          fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
		              offset: 0,
		              color: "rgba(24,39,80,0.8)"
		            },
		            {
		              offset: 1,
		              color: "rgba(20,33,66,0.5)"
		            },
		          ]),
		          text: '',
		          shadowBlur: 4,
		          shadowColor: 'rgba(58,101,156,0.3)',
		          shadowOffsetY: -25
		        },
		      },
		      {
		        type: "topShape",
		        shape: {
		          topBasicsYAxis,
		          basicsXAxis,
		          bottomYAxis,
		        },
		        style: {
		          ...api.style(),
		          fill: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
		              offset: 0,
		              color: "rgba(58,101,156,0.6)"
		            },
		            {
		              offset: 0.5,
		              color: "rgba(117, 202, 255, 0.9) "
		            },
		            {
		              offset: 1,
		              color: "rgba(58,101,156,0.6)"
		            },
		          ]),
		        },
		      },
		    ],
		  };
		},
	  }
	}
</script>

<style scoped>
	::-webkit-scrollbar {
		width: 0;
	}
	.app {		
		height: calc(100vh - 50px);
		background-color: #0F131B;
		overflow-y: hidden;
	}
	.left_div {
		float: left;		
		margin: 62px 5px 20px 48px;
		width: 300px;
		height: 100%;
	}
	.left_div_1 {
		height: 266px;
		background: url("../assets/images/home/<USER>") no-repeat;
		background-size: 305px auto;
	}
	
	.left_div_2 {
		height: 266px;
		background: url("../assets/images/home/<USER>") no-repeat;
		background-size: 305px auto;
	}
	.left_div_3 {
		height: 266px;
		background: url("../assets/images/home/<USER>") no-repeat;
		background-size: 305px auto;
	}
	.left2_div {
		float: left;
		width: 1450px;
		margin-left: 58px;
	}
	.left2_div_img {
		height: 510px;
		margin-top: 50px;
	}
	.left2_div_table {
		display: grid;
		width: 642px;
	}
	.left2_div_table_1 {
		color: #fff;
		border-collapse: separate;
		border-spacing: 0px 5px;
	}
	.left2_div_table_1 tr td {
		background-color: #0f131c;
		border-bottom: #232c42 1px solid;
		border-top: #232c42 1px solid;
	}
	tbody tr:hover td {
		background-color: #496BBA;
	}
	.left2_div_table_2 {
		color: #fff;
		border-collapse: separate;
		border-spacing: 0px 5px;
	}
	.left2_div_table_2 tr td {
		background-color: #0f131c;
		border-bottom: #232c42 1px solid;
		border-top: #232c42 1px solid;
	}
	thead {
	  position: sticky;
	  top: 0; 
	}
	.left2_div2_table {
		display: grid;	
		width: 642px;
		margin-left: 100px;
	}
	.right_div {
		position: absolute;
		width: 420px;
		height: 500px;
		right: 84px;
		top: 110px;
		color: #fff;
	}
	.right_div_1 {
		height: 260px;
		background: url("../assets/images/home/<USER>") no-repeat;
	}
	.right_div_2 {
		margin-top: 10px;
		height: 260px;
		background: url("../assets/images/home/<USER>") no-repeat;
	}
	
	
</style>