﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="23px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter x="545px" y="425px" width="24px" height="23px" filterUnits="userSpaceOnUse" id="filter342">
      <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetInner" />
      <feGaussianBlur stdDeviation="2.5" in="shadowOffsetInner" result="shadowGaussian" />
      <feComposite in2="shadowGaussian" operator="atop" in="SourceAlpha" result="shadowComposite" />
      <feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 0.698039215686274 0  " in="shadowComposite" />
    </filter>
    <g id="widget343">
      <path d="M 556 429  C 559.92 429  563 431.86  563 435.5  C 563 439.14  559.92 442  556 442  C 552.08 442  549 439.14  549 435.5  C 549 431.86  552.08 429  556 429  Z " fill-rule="nonzero" fill="#d9001b" stroke="none" />
      <path d="M 556 429.5  C 559.64 429.5  562.5 432.14  562.5 435.5  C 562.5 438.86  559.64 441.5  556 441.5  C 552.36 441.5  549.5 438.86  549.5 435.5  C 549.5 432.14  552.36 429.5  556 429.5  Z " stroke-width="1" stroke="#ffffff" fill="none" />
    </g>
  </defs>
  <g transform="matrix(1 0 0 1 -545 -425 )">
    <use xlink:href="#widget343" filter="url(#filter342)" />
    <use xlink:href="#widget343" />
  </g>
</svg>