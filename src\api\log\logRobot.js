import request from '@/utils/request'

const add = (params) =>{
	return request.post('logRobot/add', params)
}

const remove = (ids) =>{
	return request.get('logRobot/remove/' + ids)
}

const edit = (params) =>{
	return request.post('logRobot/edit', params)
}

const getList = (params) =>{
	return request.get('logRobot/getList', {params: params})
}

const getAll = (params) =>{
	return request.get('logRobot/getAll', {params: params})
}

const getLogTypeList = () =>{
	return request.get('logRobot/getLogTypeList')
}

const getOne = (id) =>{
	return request.get('logRobot/getOne/' + id)
}

const fault = (params) =>{
	return request.post('logRobot/fault', params)
}

const resetRobot = (params) =>{
	return request.post('logRobot/resetRobot', params)
}

/**
 * 切换工作模式
 * @param {robotId, workMode, autoTimeout} params
 *  robotId:机器人id
 *  workMode:工作模式 101自动模式|102遥控模式
 *  autoTimeout:自动超时时间 true遥控模式下自动超时恢复
 * @returns true成功
 */
const switchWorkMode = (params) =>{
	return request.post('dispatch/switchWorkMode', params)
}

export default {
	add, remove, edit, getList, getAll, getOne, getLogTypeList, fault, resetRobot, switchWorkMode
}