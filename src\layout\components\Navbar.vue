<template>
	<div class="navbar">


		<div class="right-menu">

			<el-dropdown @visible-change="visibleChange" @command="handleCommand" class="avatar-container right-menu-item hover-effect" trigger="hover">
				<div class="avatar-wrapper">
					<img :src="userStore.avatar" class="user-avatar" />
					<span class="user-nickname"> {{ userStore.nickName }} </span>
				</div>
				<template #dropdown>
					<el-dropdown-menu>
						<router-link to="/user/profile">
							<el-dropdown-item>个人中心</el-dropdown-item>
						</router-link>
						<el-dropdown-item divided command="logout">
							<span>退出登录</span>
						</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>


		</div>
	</div>
</template>

<script setup>
	import {
		ElMessageBox
	} from 'element-plus'
	import Breadcrumb from '@/components/Breadcrumb'
	import TopNav from '@/components/TopNav'
	import Hamburger from '@/components/Hamburger'
	import Screenfull from '@/components/Screenfull'
	import SizeSelect from '@/components/SizeSelect'
	import HeaderSearch from '@/components/HeaderSearch'
	import HycGit from '@/components/Hyc/Git'
	import HycDoc from '@/components/Hyc/Doc'
	import useAppStore from '@/store/modules/app'
	import useUserStore from '@/store/modules/user'
	import useSettingsStore from '@/store/modules/settings'

	const appStore = useAppStore()
	const userStore = useUserStore()
	const settingsStore = useSettingsStore()

	function toggleSideBar() {
		appStore.toggleSideBar()
	}

	function handleCommand(command) {
		switch (command) {
			case "setLayout":
				setLayout()
				break
			case "logout":
				setTimeout(()=>{					
					hidPlugin()
				}, 500)
				logout()
				break
			default:
				break
		}
	}
	
	function visibleChange(visible) {		
		if(visible) {
			hidPlugin()
		}else {
			showPlugin()
		}
	}
	
	function showPlugin() {
		WebVideoCtrl.I_ShowPlugin().then(() => {
			console.log('展示成功！')
		}).catch(() => {
			console.log('展示失败！')
		})
	
	}
	function hidPlugin() {
		WebVideoCtrl.I_HidPlugin().then(() => {
			console.log('隐藏成功！')
		}).catch(() => {
			console.log('隐藏失败！')
		})
	
	}

	function logout() {
		ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			userStore.logOut().then(() => {
				location.href = '/index'
			})
		}).catch(() => {
			showPlugin()
		})
	}

	const emits = defineEmits(['setLayout'])

	function setLayout() {
		emits('setLayout')
	}

	function toggleTheme() {
		settingsStore.toggleTheme()
	}
</script>

<style lang='scss' scoped>
	.navbar {
		z-index: 9999;
		position: absolute;
		right: 80px;
		height: 50px;
		top: 17px;
		overflow: hidden;

		.hamburger-container {
			line-height: 46px;
			height: 100%;
			float: left;
			cursor: pointer;
			transition: background 0.3s;
			-webkit-tap-highlight-color: transparent;

			&:hover {
				background: rgba(0, 0, 0, 0.025);
			}
		}

		.breadcrumb-container {
			float: left;
		}

		.topmenu-container {
			position: absolute;
			left: 50px;
		}

		.errLog-container {
			display: inline-block;
			vertical-align: top;
		}

		.right-menu {
			float: right;
			height: 100%;
			line-height: 50px;
			display: flex;

			&:focus {
				outline: none;
			}

			.right-menu-item {
				display: inline-block;
				padding: 0 8px;
				height: 100%;
				font-size: 18px;
				color: #5a5e66;
				vertical-align: text-bottom;

				&.hover-effect {
					cursor: pointer;
					transition: background 0.3s;

					&:hover {
						background: rgba(0, 0, 0, 0.025);
					}
				}

				&.theme-switch-wrapper {
					display: flex;
					align-items: center;

					svg {
						transition: transform 0.3s;

						&:hover {
							transform: scale(1.15);
						}
					}
				}
			}

			.avatar-container {
				margin-right: 0px;
				padding-right: 0px;

				.avatar-wrapper {
					margin-top: 10px;
					right: 5px;
					position: relative;					

					.user-avatar {
						cursor: pointer;
						width: 15px;
						height: 15px;
						border-radius: 50%;
					}

					.user-nickname {
						position: relative;
						left: 5px;
						bottom: 3px;
						font-size: 14px;
						font-weight: bold;
						color: #BFCBD9;
					}

					i {
						cursor: pointer;
						position: absolute;
						right: -20px;
						top: 25px;
						font-size: 12px;
					}
				}
			}
		}
	}
</style>