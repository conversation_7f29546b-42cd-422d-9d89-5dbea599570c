## Websocket

### Websocket后端

1. RobotWebSocketConfig

```java
@Configuration
@EnableWebSocket
public class RobotWebSocketConfig implements WebSocketConfigurer {

    @Resource
    private TokenService tokenService;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(myWebSocketHandler(), "/wsDispatch")
                .setAllowedOrigins("*")
                .addInterceptors(new AuthHandshakeInterceptor(tokenService)); // 添加握手拦截器
    }

    @Bean
    public WebSocketHandler myWebSocketHandler() {
        return new RobotWebSocketHandler();
    }
}
```

2. 发送消息给客户端

```java

/**
 * 发送消息给指定用户
 */
public static void sendMessageToUser(long userId, WebSocketPayload payload) {
    WebSocketSession session = userSessions.get(userId);
    if (session != null && session.isOpen()) {
        try {
            session.sendMessage(new TextMessage(JSON.toJSONString(payload)));
        } catch (Exception e) {
            log.error("发送消息给用户{}失败: {}", userId, e.getMessage());
        }
    }
}


/**
 * 向当前地图的所有用户广播消息
 *
 * @param payload 消息负载
 */
public static void sendMessage(long mapId, WebSocketPayload payload) {
    for (WebSocketSession session : userSessions.values()) {
        if (session.isOpen() && mapId == __getMapId(session)) {
            try {
                session.sendMessage(new TextMessage(JSON.toJSONString(payload)));
            } catch (Exception e) {
                log.error("发送消息失败: {}", e.getMessage());
            }
        }
    }
}
```

3. WebSocketPayload报文结构

```java
/**
 * WebSocket消息负载
 */
@Data
public class WebSocketPayload {
    /**
     * 消息类型：MAP地图信息 | OBSTACLE更新障碍 | ROBOT机器人状态 | AI_ALARM ai报警信息
     */
    private String type;

    /** 消息内容 */
    private Object data;

    /** 时间戳 */
    private Long timestamp;
}
```

4. 推送地图数据的场景

```java

// region 场景① 地图更新时

/**
 * 地图更新时推送
 */
public void sendMapInfo(long mapId) {
    MapContext mc = MapContext.get(mapId);
    SendMapInfoPayload payload = __loadMapInfoPayload(mc);
    sendMessage(mc.getMapId(), new WebSocketPayload("MAP", payload));
}

// endregion


// region 场景② 连接后

/**
 * 连接后第一时间推送地图信息，包括：
 * 点位（含类型）、障碍、临时障碍
 */
public void sendMapInfo(long userId, @NonNull WebSocketSession session) {
    if (session.isOpen()) {
        long mapId = __getMapId(session);
        MapContext mc = MapContext.get(mapId);
        SendMapInfoPayload payload = __loadMapInfoPayload(mc);
        sendMessageToUser(userId, new WebSocketPayload("MAP", payload));
    }
}

// endregion


// region 场景③ 障碍变更时

public void sendObstacleChange(long mapId) {
    List<MapObstacle> obstacles = mapObstacleService.getObstaclesByMapId(mapId);
    Set<String> set = obstacles.stream()
            .map(MapObstacle::getXmapPathId).collect(Collectors.toSet());
    // 空也要推，让客户端把障碍清除掉
    sendMessage(mapId, new WebSocketPayload("OBSTACLE", set));
}

// endregion


// region 场景④ AI报警，或AI报警确认时推送

public void sendAiAlarmChange(long mapId) {
    List<AlarmAi> unconfirmedAlarms = alarmAiService.getUnconfirmedAlarms(mapId);
    List<AiAlarmInfo> ai = new ArrayList<>();
    unconfirmedAlarms.forEach(a -> {
        AiAlarmInfo i = AiAlarmInfo.convertFrom(a);
        if (i != null)
            ai.add(i);
    });
    // 空也要推，让客户端把AI报警清除掉
    /**
     * AiAlarmInfo json: {"name":"","x":0.0,"y":0.0,"type":"leak","time":"2025-03-06 11:42:27","dept":"ETCH"}
     */
    sendMessage(mapId, new WebSocketPayload("AI_ALARM", ai));
}

// endregion
```

5. SendMapInfoPayload报文结构

* 放入WebSocketPayload.data中
* 主要的数据都在这里

```java

/**
 * WebSocket推往前端的地图数据：仅包含点位（含类型）、障碍、临时障碍
 */
@Data
public class SendMapInfoPayload implements Serializable {

    /**
     * 障碍点
     * json: [{"x":0.0,"y":0.0,"xmapId":"1","robotId":1,"type":0}]
     * x: x坐标
     * y: y坐标
     * xmapId: 障碍所在的xmap路径id
     * robotId: 障碍发现的机器人id
     * type: 障碍类型: 0普通障碍 | 1漏液（暂时不在前端页面显示）
     */
    private List<FlatObstacle> o;

    /**
     * 路径：
     * 第一层：[]
     * 第二层：
     * [id,xmap_id,type,dc,len,rad,sp[],ep[],c1[],c2[]]
     * example: [1,"1",0,-1,8.4,0.0,[],[],[],[]]
     * 第三层（sp/ep/c1/c2是[]，其他是标准数据类型）：
     * sp/ep: [xmap_id,biz_id,x,y,angle,is_cross]
     * sp/ep example: ["1",1,23.4,56.3,0.0,0]
     * c1/c2: [x,y,angle]
     * c1/c2: [23.4,56.3,0.0]
     * <p>
     * id: 路径id
     * xmap_id: 路径xmap id
     * type: 路径类型: 0直线|1贝塞尔曲线|2凸弧线|3凹弧线
     * dc: 1正走 | -1倒走
     * len: start至end的长度，单位：米
     * rad: 弧形路径的弧度值(仅弧线类型需要),单位:rad
     * sp: 路径起点
     * ep: 路径终点
     * c1: 贝塞尔路径的控制点1坐标
     * c2: 贝塞尔路径的控制点2坐标
     * <p>
     * sp/ep:<br/>
     * xmap_id: 路径点xmap id
     * biz_id: 业务点位id（可能不是业务点位则为null）
     * x: x坐标
     * y: y坐标
     * angle: 朝向角
     * is_cross: 是否是十字路口: 1是|0否
     */
    private List<List<Object>> pts;

    /**
     * 点位：
     * [[xmap_id,biz_id,x,y,angle,is_cross,class_name,instance_name]]
     * ["1",1,23.4,56.3,0.0,0,"LandMark","标记点A"]
     * <p>
     *     xmap_id: 点位xmap id
     *     biz_id: 业务点位id（null则表示非业务点位，仅仅只是地图的点位而已）
     *     x: x坐标
     *     y: y坐标
     *     angle: 朝向角
     *     is_cross: 是否是十字路口: 1是|0否
     *     class_name: 点位类型：LandMark(标记点) | ChargePoint(充电点) | IdenPoint(识别) | InfraredDetection(红外测温) | Crossroads(十字路口) | Exit(出口) | Elevator(电梯) | EyeWashStation(紧急洗眼器) | Parking(停车位)
     *     instance_name: 点位名称
     */
    private List<List<Object>> ps;

    /**
     * 当前地图上所有未确认的AI报警
     * json: {"name":"","x":0.0,"y":0.0,"type":"leak","time":"2025-03-06 11:42:27","dept":"ETCH"}
     */
    private List<AiAlarmInfo> ai;
}
```

```java
/**
 * AI报警信息，Websocket传输对象
 */
@Data
public class AiAlarmInfo implements Serializable {
    /** 报警描述 **/
    private String name;
    /** 坐标 **/
    private Double x;
    /** 坐标 **/
    private Double y;
    /** leak漏液|matter异物|gas气体传感器|temp温度传感器 */
    private String type;
    /** 报警时间 **/
    private Date time;
    /** 部门名称 **/
    private String dept;
}
```

6. 每秒实时通过websocket推送机器人当前位置及状态

```java
// region 机器人实时状态推送
/**
 * 1秒一次推送
 */
@Scheduled(fixedDelay = 1000)
public void sendRobotStatus() {
    if (!ContextLoader.isLoaded()) return;

    MapContext mc = __getMapContext();
    if (mc == null) return;

    if (RobotContext.values().isEmpty()) {
        sendMessage(mc.getMapId(), new WebSocketPayload("ROBOT", Collections.EMPTY_LIST));
    } else {
        List<SendRobotStatusPayload> payload = __loadData(mc);
        sendMessage(mc.getMapId(), new WebSocketPayload("ROBOT", payload));
    }
}

// endregion
```

7. SendRobotStatusPayload报文结构

```java 
/**
 * WebSocket推往前端的机器人状态数据
 *
 * <AUTHOR>
 */
@Data
public class SendRobotStatusPayload implements Serializable {

    /** 机器人id **/
    private long id;
    /** 机器人名称 **/
    private String name;
    /** 机器人序列号 **/
    private String sn;
    /** 机器人当前位置 **/
    private Position pos;
    /** 机器人当前朝向（弧度rad） **/
    private double theta;
    /** 机器人当前所在路径 **/
    private String xpId;
    /** 机器人当前任务id **/
    private Long tkId;
    /** 机器人当前任务名称 **/
    private String tkName;
    /** 机器人当前速度（米/秒） **/
    private double vel = 1.0;
    /** 机器人当前定位置信度（0.0-1.0） **/
    private double rel = 1.0;
    /** 机器人累计运行时间 **/
    private String tr;
    /** 机器人累计里程数（KM） **/
    private String to;
    /** 机器人当前电量 **/
    private String bty;
    /** 机器人状态 **/
    private String s;
    /** 机器人工作状态 **/
    private String ws;
    /** 机器人工作模式 **/
    private String wm;
    /** 已走完的路径 **/
    private List<IPath> cps;
    /** 当前目标点位 **/
    private IPosition tp;
    /** 颜色 **/
    private String clr;
    /** 是否在路径上 **/
    private Boolean isOnPath;

    private final static long serialVersionUID = 1L;
}


public interface IPath extends Serializable, Cloneable {

    /**
     * 路径ID
     */
    String getXmapId();

    /**
     * 路径名称
     */
    String getInstanceName();

    /**
     * 路径类型
     * bezier_curve - 贝塞尔曲线
     * straight_line - 直线
     * convex - 凸弧线
     * concave_arc - 凹弧线
     */
    String getRouteType();

    /**
     * 弧形路径的弧度值(仅弧线类型需要),单位:rad
     */
    double getRadian();

    /**
     * 起点坐标
     */
    IPosition getStartPos();

    /**
     * 终点坐标
     */
    IPosition getEndPos();

    /**
     * 路径控制点1坐标
     */
    IPosition getCtrlPos1();

    /**
     * 路径控制点2坐标
     */
    IPosition getCtrlPos2();

    /**
     * 角度补偿
     */
    double getAngleCompensation();

    /**
     * 方向: forward 表示正走，backward 表示倒走
     * 往返可走的路径是两条线，一条正走，一条倒走，不存在both类型
     */
    String getDirection();

    /**
     * 路径长度，单位同坐标单位（m）
     */
    double getLength();
}
```



### Websocket前端

```javascript
import { getToken } from '@/utils/auth'

config.headers['Authorization'] = 'Bearer ' + getToken()

// 连接需要传递token进去
const token = 'Bearer ' + getToken();
const socket = new WebSocket(`ws://${location.host}/ws`, ["Authorization", token]);


// 监听连接成功
socket.onopen = () => {
    console.log("WebSocket 连接成功");
};

// 监听消息
socket.onmessage = (event) => {
    console.log("收到消息:", event.data);
};

// 监听连接关闭
socket.onclose = () => {
    console.log("WebSocket 连接关闭");
};


// 监听页面关闭事件
window.addEventListener("beforeunload", () => {
    if (socket.readyState === WebSocket.OPEN) {
        socket.close(1000, "用户离开页面"); // 1000 是正常关闭状态码
    }
});
```



### 图标

#### 机器人图标

* 旋转头朝向到某个角度：

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" width="200" height="200"><g transform="rotate(90, 0, 0) translate(0 -240) scale(0.5 0.5)"><path d="M54.2 202.9C123.2 136.7 216.8 96 320 96s196.8 40.7 265.8 106.9c12.8 12.2 33 11.8 45.2-.9s11.8-33-.9-45.2C549.7 79.5 440.4 32 320 32S90.3 79.5 9.8 156.7C-2.9 169-3.3 189.2 8.9 202s32.5 13.2 45.2 .9zM320 256c56.8 0 108.6 21.1 148.2 56c13.3 11.7 33.5 10.4 45.2-2.8s10.4-33.5-2.8-45.2C459.8 219.2 393 192 320 192s-139.8 27.2-190.5 72c-13.3 11.7-14.5 31.9-2.8 45.2s31.9 14.5 45.2 2.8c39.5-34.9 91.3-56 148.2-56zm64 160a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z" fill="#AFAFAF" p-id="57648"/></g></svg>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" width="200" height="200"><g transform="rotate(180, 0, 0) translate(-320 -240) scale(0.5 0.5)"><path d="M54.2 202.9C123.2 136.7 216.8 96 320 96s196.8 40.7 265.8 106.9c12.8 12.2 33 11.8 45.2-.9s11.8-33-.9-45.2C549.7 79.5 440.4 32 320 32S90.3 79.5 9.8 156.7C-2.9 169-3.3 189.2 8.9 202s32.5 13.2 45.2 .9zM320 256c56.8 0 108.6 21.1 148.2 56c13.3 11.7 33.5 10.4 45.2-2.8s10.4-33.5-2.8-45.2C459.8 219.2 393 192 320 192s-139.8 27.2-190.5 72c-13.3 11.7-14.5 31.9-2.8 45.2s31.9 14.5 45.2 2.8c39.5-34.9 91.3-56 148.2-56zm64 160a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z" fill="#AFAFAF" p-id="57648"/></g></svg>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" width="200" height="200"><g transform="rotate(-90, 0, 0) translate(-320 -16) scale(0.5 0.5)"><path d="M54.2 202.9C123.2 136.7 216.8 96 320 96s196.8 40.7 265.8 106.9c12.8 12.2 33 11.8 45.2-.9s11.8-33-.9-45.2C549.7 79.5 440.4 32 320 32S90.3 79.5 9.8 156.7C-2.9 169-3.3 189.2 8.9 202s32.5 13.2 45.2 .9zM320 256c56.8 0 108.6 21.1 148.2 56c13.3 11.7 33.5 10.4 45.2-2.8s10.4-33.5-2.8-45.2C459.8 219.2 393 192 320 192s-139.8 27.2-190.5 72c-13.3 11.7-14.5 31.9-2.8 45.2s31.9 14.5 45.2 2.8c39.5-34.9 91.3-56 148.2-56zm64 160a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z" fill="#AFAFAF" p-id="57648"/></g></svg>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" width="200" height="200"><g transform="scale(0.5 0.5)"><path d="M54.2 202.9C123.2 136.7 216.8 96 320 96s196.8 40.7 265.8 106.9c12.8 12.2 33 11.8 45.2-.9s11.8-33-.9-45.2C549.7 79.5 440.4 32 320 32S90.3 79.5 9.8 156.7C-2.9 169-3.3 189.2 8.9 202s32.5 13.2 45.2 .9zM320 256c56.8 0 108.6 21.1 148.2 56c13.3 11.7 33.5 10.4 45.2-2.8s10.4-33.5-2.8-45.2C459.8 219.2 393 192 320 192s-139.8 27.2-190.5 72c-13.3 11.7-14.5 31.9-2.8 45.2s31.9 14.5 45.2 2.8c39.5-34.9 91.3-56 148.2-56zm64 160a64 64 0 1 0 -128 0 64 64 0 1 0 128 0z" fill="#AFAFAF" p-id="57648"/></g></svg>

* 机器人robot：

<svg t="1751617521095" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="66802" width="200" height="200" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M818.2 739.2h-193c-15.4 0-28-12.6-28-28v-31.3h166.3c15.4 0 28-12.6 28-28V207.7c0-15.4-12.6-28-28-28H625.2v-59.6c0-30.9-25.2-56.1-56.1-56.1H456c-30.9 0-56.1 25.2-56.1 56.1v59.6H256.8c-15.4 0-28 12.6-28 28v444.1c0 15.4 12.6 28 28 28H428v31.3c0 15.4-12.6 28-28 28H202.6c-15.4 0-28 12.6-28 28V932c0 15.4 12.6 28 28 28h615.7c15.4 0 28-12.6 28-28V767.2c0-15.4-12.7-28-28.1-28zM681 427.4c0 31-25.1 56.1-56.1 56.1s-56.1-25.1-56.1-56.1 25.1-56.1 56.1-56.1 56.1 25.1 56.1 56.1zM456 120.1c0.1 0 0.1 0 0 0h113v59.5H456v-59.5zM346.5 427.4c0-31 25.1-56.1 56.1-56.1s56.1 25.1 56.1 56.1-25.1 56.1-56.1 56.1c-30.9 0-56.1-25.1-56.1-56.1z m530.1-115.7v231.4c0 15.5-12.6 28-28 28-15.5 0-28-12.6-28-28V311.7c0-15.5 12.6-28 28-28s28 12.6 28 28z m83.6 57.9v112.3c0 15.5-12.6 28-28 28-15.5 0-28-12.6-28-28V369.6c0-15.5 12.6-28 28-28 15.5 0 28 12.5 28 28zM175.5 571.1c-15.5 0-28-12.6-28-28V311.7c0-15.5 12.6-28 28-28 15.5 0 28 12.6 28 28v231.4c0 15.5-12.5 28-28 28z m-55.7-201.5v112.3c0 15.5-12.6 28-28 28-15.5 0-28-12.6-28-28V369.6c0-15.5 12.6-28 28-28 15.5 0 28 12.5 28 28z m0 0" p-id="66803" fill="#AFAFAF"></path></svg>

#### 点位图标

* 标记点 LandMark：

<svg t="1751594188433" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="29543" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M522.528821 24.996103A393.058462 393.058462 0 0 0 243.186872 137.819897 392.664615 392.664615 0 0 0 125.846974 415.245128a377.409641 377.409641 0 0 0 71.548718 224.282257c36.890256 52.696615 116.184615 126.687179 166.386872 184.582564 50.228513 55.401026 137.531077 166.203077 156.041846 187.286974 2.70441 2.573128 2.70441 2.573128 5.303795 0 18.510769-23.709538 134.957949-160.899282 148.138667-179.383795 15.780103-18.379487 121.619692-126.60841 166.518154-184.582564a389.697641 389.697641 0 0 0 76.72123-234.679795C914.353231 197.185641 738.225231 23.762051 522.502564 24.996103z m0 551.253333a167.988513 167.988513 0 0 1-169.222565-166.071795A167.909744 167.909744 0 0 1 522.502564 244.07959a167.988513 167.988513 0 0 1 169.353846 166.098051c-1.129026 92.501333-76.826256 166.859487-169.353846 166.071795z" p-id="29544" fill="#707070"></path></svg>

* 充电点 ChargePoint：

<svg t="1751595730021" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="57645" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M772.425 124.088c10.799 0 19.998 11.799 19.998 25.697v750.227H232.477V149.885c0-13.998 9.2-25.697 19.998-25.697h519.95m0-60.094h-519.95c-44.195 0-79.992 38.396-79.992 85.691v750.227H64.493v59.994h895.913v-59.994h-107.99V149.885c0-47.395-35.796-85.791-79.991-85.791z m-89.992 179.982v79.992H342.467v-79.992h339.966m59.994-59.994H282.472v199.98h459.955v-199.98zM392.462 683.033H512.45V443.057zM512.45 613.04v239.977L632.438 613.04z" fill="#707070" p-id="57646"></path></svg>

* 识别点 IdenPoint：

<svg t="1751591701319" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5704" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M511.06304 14.11584c-206.42304 0-373.76 167.33696-373.76 373.76s167.33696 373.76 373.76 373.76 373.76-167.33696 373.76-373.76-167.33184-373.76-373.76-373.76z m0 624.33792c-138.38848 0-250.5728-112.18432-250.5728-250.5728s112.18432-250.5728 250.5728-250.5728 250.5728 112.18432 250.5728 250.5728-112.1792 250.5728-250.5728 250.5728z" fill="#707070" p-id="5705"></path><path d="M259.23584 663.50592l514.96448-10.33216-269.65504 349.88032zM513.28 273.65888c-104.19712 0-216.33024 112.90112-216.33024 112.90112s112.13312 112.90112 216.33024 112.90112c104.19712 0 216.33024-112.90112 216.33024-112.90112s-112.13312-112.90112-216.33024-112.90112z m2.304 190.52544c-43.9296 0-79.5392-35.6096-79.5392-79.5392S471.6544 305.1008 515.584 305.1008c43.92448 0 79.5392 35.6096 79.5392 79.5392s-35.61472 79.54432-79.5392 79.54432z" fill="#707070" p-id="5706"></path><path d="M515.584 384.64m-49.57184 0a49.57184 49.57184 0 1 0 99.14368 0 49.57184 49.57184 0 1 0-99.14368 0Z" fill="#707070" p-id="5707"></path></svg>

* 红外测温 InfraredDetection：

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="200" height="200"><path d="M448 96a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM320 96a96 96 0 1 1 192 0A96 96 0 1 1 320 96zM144 64c-26.5 0-48 21.5-48 48l0 164.5c0 17.3-7.1 31.9-15.3 42.5C70.2 332.6 64 349.5 64 368c0 44.2 35.8 80 80 80s80-35.8 80-80c0-18.5-6.2-35.4-16.7-48.9c-8.2-10.6-15.3-25.2-15.3-42.5L192 112c0-26.5-21.5-48-48-48zM32 112C32 50.2 82.1 0 144 0s112 50.1 112 112l0 164.4c0 .1 .1 .3 .2 .6c.2 .6 .8 1.6 1.7 2.8c18.9 24.4 30.1 55 30.1 88.1c0 79.5-64.5 144-144 144S0 447.5 0 368c0-33.2 11.2-63.8 30.1-88.1c.9-1.2 1.5-2.2 1.7-2.8c.1-.3 .2-.5 .2-.6L32 112zM192 368c0 26.5-21.5 48-48 48s-48-21.5-48-48c0-20.9 13.4-38.7 32-45.3l0-50.7c0-8.8 7.2-16 16-16s16 7.2 16 16l0 50.7c18.6 6.6 32 24.4 32 45.3z" fill="#707070" p-id="44076"/></svg>

* 十字路口 Crossroads：

<svg t="1751591523776" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4633" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M682.662 1024H341.338V820.454c0-69.533-68.38-137.777-138.053-137.777H0.015V341.323h203.516c69.548 0 137.807-67.635 137.807-136.548V0h341.324v209.66c0 66.451 67.346 131.663 135.964 131.663h205.359v341.354h-207.14c-63.951 0-134.183 70.612-134.183 134.92V1024zM580.273 682.677v102.392h68.26V682.677z m-102.386 0v102.392h68.258V682.677h-68.258z m-102.423 0v102.392h68.259V682.677h-68.255zM682.66 580.255v68.258h102.422v-68.255z m-443.711 0v68.258h102.39v-68.255H238.95zM682.662 477.87v68.26h102.422v-68.26z m-443.712 0v68.26h102.388v-68.26H238.95z m443.712-102.387v68.262h102.422v-68.262z m-443.712 0v68.262h102.388v-68.262H238.95z m341.323-136.547v102.387h68.26V238.935z m-102.386 0v102.387h68.258V238.935h-68.258z m-102.423 0v102.387h68.259V238.935h-68.255z" fill="#707070" p-id="4634"></path></svg>

* 出口 Exit：

<svg t="1751594417542" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="44074" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M700.6 676.7l200.8-157.4c5.2-4 5.2-10.6 0-14.6L700.6 347.3c-2.6-2-6-3-9.4-3v115H489.6c-12.3 0-22.3 10-22.3 22.3v60.8c0 12.3 10 22.3 22.3 22.3h201.6v115c3.4 0 6.8-1 9.4-3zM588.8 788H205.3V236h383.5v63.2H730V94.7H64v834.6h666V729.5H588.8V788zM960 441.4l-156.3-122-19.9 25.6 156.3 122 19.9-25.6zM783.8 675.9l19.9 25.6 156.3-122-19.9-25.6-156.3 122z" fill="#707070" p-id="44075"></path></svg>

* 停车位 Parking：

<svg t="1751619435322" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="69071" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M511.948876484375 139.40946054687504c-157.07214534374995 0-284.3535594375 125.56139498437499-284.3535594375 280.500716671875s127.28141409374999 280.43191575 284.3535594375 280.43191575 284.3535594375-125.56139498437499 284.3535594375-280.43191575S669.021021828125 139.40946054687504 511.948876484375 139.40946054687504zM178.19636735937507 660.0248442968751c-5.0912564062499985-6.880076437499998-12.1089346875-16.099379296875-17.7505965-25.38748209375C120.67892857812512 571.684663390625 94.32823554687502 499.58146165625 94.32823554687502 419.91017721875005 94.32823554687502 192.38604832812507 281.2599130625001 8 511.8800755625 8s417.6206399531249 184.386048328125 417.6206399531249 411.91017721874994a404.47969439062496 404.47969439062496 0 0 1-66.66794071874999 222.364070578125 264.3325374375 264.3325374375 0 0 1-28.621117546874995 30.685140281249996C822.7907298124999 685.3435264531252 555.29335784375 993.1581461093749 555.29335784375 993.1581461093749c-11.214524671875 13.760152874999996-23.461060218749996 22.841853890624996-36.80840953124999 22.841853890624996s-23.323459359375-10.526517421874997-33.505972171875-22.841853890624996c0 0-301.69135237499995-326.04682260937494-306.78260878125-333.1333018125z" fill="#707070" p-id="69072"></path><path d="M518.828952921875 334.1844244531251h-26.213091187499998v90.67940732812497h26.213091187499998q52.77018599999999 0 52.77018599999999-45.890109562499994-0.344003625-44.78929776562499-52.77018599999999-44.78929776562499z" fill="#707070" p-id="69073"></path><path d="M511.948876484375 194.1060674375001a225.46010517187497 225.46010517187497 0 1 0 225.46010517187497 225.46010517187497A225.46010517187497 225.46010517187497 0 0 0 511.948876484375 194.1060674375001z m90.61060640624999 251.67319635937494a115.99808849999998 115.99808849999998 0 0 1-79.12087903124997 24.905877609374997h-30.822742124999998v97.903487390625H432.827997453125V287.812709421875h96.32107012499998q104.78356382812498 0 104.78356382812498 88.821786375a85.03774453124998 85.03774453124998 0 0 1-31.373148515624994 69.144768z" fill="#707070" p-id="69074"></path></svg>

#### 障碍

* 障碍点 obstacle：

<svg t="1751595063989" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="46064" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M239.936 64c15.424 0 27.968 12.544 27.968 27.968v45.952h388.736a27.072 27.072 0 0 1 8.512 0h90.88v-45.952c0.064-15.424 12.544-27.968 27.968-27.968h148.032c15.424 0 27.968 12.544 27.968 27.968V240a28.224 28.224 0 0 1-27.968 27.968h-46.08v92.416a14.4 14.4 0 0 1 0 2.816v392.96h46.016c15.424 0 27.968 12.48 27.968 27.904v147.84a27.968 27.968 0 0 1-27.968 28.032h-148.096a27.968 27.968 0 0 1-27.904-28.032v-45.952H267.904v45.952A28.224 28.224 0 0 1 240 960H91.968A27.968 27.968 0 0 1 64 932.032v-147.968c0-15.424 12.544-27.904 27.968-27.904h46.016v-488.32h-46.08A27.968 27.968 0 0 1 64 239.808V91.968C64 76.544 76.544 64 91.968 64z m590.144 570.496l-195.648 195.84h121.6v-46.08c0-15.424 12.544-27.968 27.968-27.968h46.08V634.496z m-0.128-366.528h-34.56l-527.552 527.808v34.496h82.368l479.744-479.872V267.968z m0 161.6l-400.512 400.64H556.8a28.416 28.416 0 0 1 4.928-6.656l268.16-268.224V429.568zM755.968 193.92h-83.84l-478.08 478.336H193.92v83.904h34.368l527.744-527.872v-34.368z m-163.136 0.064H465.6l-266.88 267.072a29.44 29.44 0 0 1-4.736 3.84v128.192l398.848-399.104z m-206.336 0H268.032v46.08c0 15.36-12.544 27.84-27.904 27.84h-46.08v118.72l192.448-192.64z" fill="#707070" p-id="46065"></path></svg>

#### AI报警

* 温度传感器报警 temp：

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="200" height="200"><path d="M416 64a32 32 0 1 1 0 64 32 32 0 1 1 0-64zm0 128A96 96 0 1 0 416 0a96 96 0 1 0 0 192zM96 112c0-26.5 21.5-48 48-48s48 21.5 48 48l0 164.5c0 17.3 7.1 31.9 15.3 42.5C217.8 332.6 224 349.5 224 368c0 44.2-35.8 80-80 80s-80-35.8-80-80c0-18.5 6.2-35.4 16.7-48.9C88.9 308.4 96 293.8 96 276.5L96 112zM144 0C82.1 0 32 50.2 32 112l0 164.4c0 .1-.1 .3-.2 .6c-.2 .6-.8 1.6-1.7 2.8C11.2 304.2 0 334.8 0 368c0 79.5 64.5 144 144 144s144-64.5 144-144c0-33.2-11.2-63.8-30.1-88.1c-.9-1.2-1.5-2.2-1.7-2.8c-.1-.3-.2-.5-.2-.6L256 112C256 50.2 205.9 0 144 0zm0 416c26.5 0 48-21.5 48-48c0-20.9-13.4-38.7-32-45.3L160 112c0-8.8-7.2-16-16-16s-16 7.2-16 16l0 210.7c-18.6 6.6-32 24.4-32 45.3c0 26.5 21.5 48 48 48z" fill="#707070" p-id="44077"/></svg>

* 漏液报警 leak：

<svg t="1751595182234" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="47382" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M864 880H166.4c-19.2 0-35.2-16-35.2-35.2s16-35.2 35.2-35.2H864c19.2 0 35.2 16 35.2 35.2s-19.2 35.2-35.2 35.2zM521.6 16s-12.8 54.4-76.8 147.2c-76.8 105.6-204.8 233.6-204.8 345.6 0 150.4 121.6 268.8 268.8 268.8s268.8-121.6 268.8-268.8-256-492.8-256-492.8z m169.6 604.8c-16 28.8-44.8 48-76.8 57.6H608c-12.8 0-25.6-9.6-25.6-22.4-3.2-16 6.4-28.8 19.2-32 16-3.2 32-16 38.4-28.8 9.6-16 12.8-35.2 6.4-57.6-3.2-16 6.4-28.8 19.2-32 16-3.2 28.8 6.4 32 19.2 16 35.2 9.6 70.4-6.4 96z m3.2 380.8H332.8c-19.2 0-35.2-16-35.2-35.2s16-35.2 35.2-35.2h364.8c19.2 0 35.2 16 35.2 35.2s-16 35.2-38.4 35.2z" p-id="47383" fill="#707070"></path></svg>

* 异物 matter：

<svg t="1751595473912" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="52445" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M512 0c282.752 0 512 229.248 512 512s-229.248 512-512 512S0 794.752 0 512 229.248 0 512 0z m-79.402667 213.333333h-61.866666L213.333333 810.666667h113.834667l105.386667-597.333334z m220.672 0h-61.866666l105.386666 597.333334H810.666667L653.226667 213.333333z m-43.264 479.274667c1.194667 5.973333-0.085333 6.4-4.266666 2.56 1.578667 3.712 0.256 3.157333-2.218667 1.024-0.298667 0.213333-1.706667-1.194667-3.413333-3.029333a18.218667 18.218667 0 0 1 0.085333 4.522666v-0.085333c-1.322667 1.152-2.56 1.28-3.712-0.170667l-0.853333-1.322666-0.426667 1.322666c-0.341333 1.194667-0.512 1.408-0.597333 0.768v-0.426666l-0.853334 1.152c-1.365333 1.578667-2.304 0.810667-2.944-2.688l-0.384-2.346667-0.085333 2.773333c-0.512 6.784-2.090667 6.4-4.778667-3.072 0 12.672-1.365333 13.824-7.424 0.981334 3.498667 15.36 2.346667 16.810667-12.629333 7.552 4.266667 4.010667 6.954667 6.912 8.533333 7.253333 0.554667 3.754667 0 5.034667-0.853333 0.682667l-0.426667 0.554666-0.170666 2.816a40.789333 40.789333 0 0 0 0 3.84l0.042666 0.64-1.28 1.962667c-1.962667 2.389333-4.010667 3.285333-6.826666 2.602667l-2.133334-0.768 0.938667 0.554666c7.168 3.626667 10.410667 1.109333 17.109333-6.698666-8.533333 12.458667-10.581333 14.890667-14.293333 18.773333 3.712-1.962667 5.802667-2.432 22.314667-5.546667-11.178667 3.498667-14.250667 5.546667-10.24 11.776l1.365333 1.92-0.426667-0.554666c-2.474667-2.602667-2.602667-1.92-0.554666 1.621333l0.853333 1.28c-1.024-0.768-0.853333-0.256 0.682667 1.792l1.28 1.706667c-0.170667 0.682667 0.682667 2.389333 2.048 4.864-0.170667-2.389333 1.664-1.749333 3.413333-0.682667a17.706667 17.706667 0 0 1 4.437333 4.010667 15.658667 15.658667 0 0 1 4.821334 0c2.901333 0.256 4.821333 0.682667 1.365333 1.109333 7.338667-0.426667 9.216-0.853333 0.853333-2.304 11.946667 0.469333 13.738667-0.085333 1.962667-5.376 10.069333 2.56 14.165333 3.072 13.397333-1.066667a22.186667 22.186667 0 0 0-2.986666-6.485333l-1.834667-2.986667 2.602667 2.474667c1.664 1.493333 2.986667 2.730667 4.138666 3.584l1.536 1.066667c1.92 0.981333 3.072 0.469333 3.84-1.578667 0.426667-1.066667 0.725333-2.474667 0.597334-2.688a8.96 8.96 0 0 0-0.170667-1.194667l-0.213333-0.64c0.597333 0 0.853333 0.085333 1.109333 0.256l0.597333 0.597334h0.042667l0.042667 0.128 0.597333 0.938666c0.768 0.128 1.194667 0.896 1.621333 1.024 1.28-0.725333 2.56-3.968 2.389334-1.194666 1.408-4.821333 1.28-3.712 0.426666 0.512 0.426667-1.536 0.810667-2.730667 1.408-3.072l0.085334-0.170667v-0.085333 0.298666h0.170666l0.213334 0.298667 0.085333-0.426667c-0.128-0.426667-0.213333-0.554667-0.384-0.384a2.048 2.048 0 0 1 0.64-1.066666v-0.085334l0.085333-0.256h-0.256l-0.170666 0.170667-0.298667 0.469333a10.965333 10.965333 0 0 1 0.64-1.536l0.64-0.682666 0.042667-0.128-1.28-1.28c-2.688-2.176-4.266667-0.298667-5.888 4.266666l-0.384 0.896 0.426666-1.322666c1.152-3.584 2.346667-5.973333 4.266667-6.613334l1.536-0.128 1.408 0.384c-0.213333-1.28-0.042667-1.237333 0.469333-0.256l0.256 0.512 2.048 1.194667 2.688 2.090667c-5.973333-12.970667-4.992-14.592 8.106667-10.24-11.648-6.528-11.093333-7.04 1.749333-5.12-13.354667-3.072-14.378667-3.669333 0.085334-4.096-17.024-1.493333-18.517333-2.816-8.149334-18.346667-13.653333 10.368-17.194667 8.832-23.936-2.56l-0.341333-0.512c-1.024 0.213333-2.304-0.512-4.010667-2.304z m-118.144-102.826667c1.792 8.874667-0.085333 9.472-6.272 3.84 2.346667 5.461333 0.426667 4.693333-3.285333 1.536-0.426667 0.298667-2.56-1.792-4.992-4.522666 0.512 5.418667 0.128 8.192 0 6.186666-2.133333 2.346667-4.096 2.218667-5.930667-0.426666l-0.810666-1.365334-0.469334 1.706667c-0.682667 2.218667-0.938667 2.389333-1.024 0.768l-0.896 1.365333c-2.261333 2.816-3.754667 1.706667-4.736-4.010666l-0.469333-3.114667-0.128 3.157333c-0.597333 9.984-2.56 10.794667-6.016 0l-1.066667-3.626666c0 18.773333-2.048 20.437333-11.008 1.450666 5.205333 22.741333 3.498667 24.832-18.688 11.178667 6.272 5.973333 10.325333 10.197333 12.714667 10.709333 0.768 5.546667-0.085333 7.424-1.322667 1.024l-0.725333 0.981334-0.128 3.029333a69.12 69.12 0 0 0-0.085333 6.613333l0.128 0.981334-0.597334 1.024c-3.925333 5.76-7.808 7.552-14.165333 4.778666l-0.938667-0.512 2.474667 1.408c9.088 4.394667 13.909333 1.92 21.717333-6.656l3.072-3.498666c-12.629333 18.432-15.658667 21.973333-21.205333 27.733333 5.546667-2.901333 8.576-3.626667 33.024-8.234667-17.621333 5.546667-21.589333 8.661333-13.824 19.370667l0.512 0.725333-0.853333-0.981333c-3.584-3.754667-3.328-2.048 0.810666 4.650667-1.706667-1.365333-1.194667-0.341333 1.877334 3.797333l1.024 1.365333-0.042667 0.512a27.093333 27.093333 0 0 0 3.114667 6.698667c-0.298667-3.541333 2.474667-2.56 5.034666-1.024a26.026667 26.026667 0 0 1 6.570667 5.930667 24.661333 24.661333 0 0 1 7.168-0.042667c4.266667 0.426667 7.082667 1.024 1.962667 1.706667 10.88-0.682667 13.653333-1.28 1.28-3.413334 17.706667 0.682667 20.309333-0.128 2.901333-7.936 14.933333 3.797333 20.949333 4.48 19.797333-1.578666a35.029333 35.029333 0 0 0-4.906666-10.453334l-2.261334-3.584 3.157334 2.986667c2.986667 2.773333 5.418667 4.949333 7.338666 6.4l1.834667 1.109333c2.773333 1.493333 4.48 0.768 5.674667-2.261333 0.597333-1.578667 1.066667-3.669333 0.853333-3.968-0.085333-1.152-0.213333-2.090667-0.213333-2.773333 0.469333 0 0.896 0.170667 1.28 0.426666l0.896 0.938667 0.085333-0.170667v0.213334l0.042667 0.042666 0.853333 1.408c1.152 0.256 1.749333 1.322667 2.432 1.536 1.834667-1.109333 3.84-5.845333 3.498667-1.749333 2.133333-7.168 1.92-5.546667 0.682666 0.682667a17.194667 17.194667 0 0 1 1.450667-4.138667l0.426667-0.341333 0.042666-0.341334v0.341334l0.042667-0.085334h0.128l0.426667 0.256 0.298666 0.426667 0.042667-0.682667c-0.128-0.512-0.213333-0.725333-0.554667-0.682666l0.256-0.725334c0.128-0.341333 0.256-0.597333 0.64-0.768l0.085334-0.512c-0.298667-0.085333-0.426667 0-0.554667 0.213334l-0.341333 0.64 0.298666-1.024c0.384-1.066667 0.768-1.792 1.493334-2.218667l0.085333-0.128-1.152-1.28c-4.565333-4.309333-6.997333-1.365333-9.685333 6.272l-0.682667 1.792 0.64-2.005333c2.218667-7.125333 4.394667-11.306667 8.704-11.008l2.304 0.554666c-0.341333-1.834667-0.085333-1.792 0.682667-0.298666l0.426666 0.810666 1.194667 0.554667c1.664 0.981333 3.541333 2.346667 5.76 4.181333-8.789333-19.157333-7.424-21.546667 11.989333-15.061333-17.237333-9.728-16.469333-10.496 2.56-7.594667-19.712-4.565333-21.205333-5.418667 0.128-6.101333-25.130667-2.218667-27.392-4.181333-12.032-27.093333-19.029333 14.506667-24.789333 13.226667-33.834666-1.152l-2.090667-3.413334-0.682667 0.170667c-1.28-0.128-2.986667-1.28-5.248-3.584z m55.296-306.176h-70.272v133.76h-43.946666L512 509.098667l79.061333-91.733334h-43.946666V283.605333z" fill="#707070" p-id="52446" data-spm-anchor-id="a313x.search_index.0.i22.4d163a815gP5WI" class=""></path></svg>

* 烟雾 gas: 

<svg t="1751595655345" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="56570" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M512 64C264.96 64 64 264.96 64 512s200.96 448 448 448 448-200.96 448-448S759.04 64 512 64z m0 832c-211.744 0-384-172.256-384-384s172.256-384 384-384 384 172.256 384 384-172.256 384-384 384z" p-id="56571" fill="#707070"></path><path d="M404.96 770.432a278.208 278.208 0 0 1-90.688-60.672 278.432 278.432 0 0 1-60.704-90.72 32 32 0 0 0-59.136 24.512 341.6 341.6 0 0 0 74.592 111.456 342.176 342.176 0 0 0 111.456 74.56 31.936 31.936 0 0 0 41.792-17.312 31.968 31.968 0 0 0-17.312-41.824z m-193.216-348.16a31.872 31.872 0 0 0 41.824-17.28 278.08 278.08 0 0 1 60.704-90.72 278.304 278.304 0 0 1 90.688-60.704 32 32 0 0 0-24.512-59.136 342.016 342.016 0 0 0-111.456 74.56 342.144 342.144 0 0 0-74.56 111.488 32 32 0 0 0 17.312 41.792zM619.04 253.568a278.784 278.784 0 0 1 90.72 60.704 278.56 278.56 0 0 1 60.672 90.688 32 32 0 1 0 59.136-24.512 342.176 342.176 0 0 0-74.56-111.456A342.432 342.432 0 0 0 643.552 194.4a32.032 32.032 0 0 0-24.512 59.168z m193.216 348.16a32.096 32.096 0 0 0-41.824 17.312 278.336 278.336 0 0 1-60.672 90.72 278.336 278.336 0 0 1-90.72 60.672 32 32 0 0 0 24.512 59.136 342.176 342.176 0 0 0 111.456-74.56 342.848 342.848 0 0 0 74.56-111.456 32 32 0 0 0-17.312-41.824z m-463.68-253.152a230.208 230.208 0 0 0-50.112 74.944 32 32 0 0 0 59.136 24.544 166.784 166.784 0 0 1 36.256-54.208 166.4 166.4 0 0 1 54.208-36.288 32.032 32.032 0 0 0-24.512-59.136 230.592 230.592 0 0 0-74.976 50.144z m227.392 8.992c20.288 8.416 38.528 20.64 54.176 36.256s27.84 33.888 36.288 54.208a32 32 0 1 0 59.136-24.544 230.816 230.816 0 0 0-50.144-74.944 230.4 230.4 0 0 0-74.944-50.112 32 32 0 1 0-24.512 59.136zM448.032 666.432a166.56 166.56 0 0 1-54.208-36.288 166.912 166.912 0 0 1-36.288-54.176A32 32 0 0 0 298.4 600.48a230.464 230.464 0 0 0 50.144 74.944 229.952 229.952 0 0 0 74.944 50.144 32 32 0 0 0 24.544-59.136z m227.36 8.96a230.144 230.144 0 0 0 50.144-74.944 32 32 0 1 0-59.136-24.512c-8.416 20.288-20.64 38.528-36.288 54.176s-33.92 27.872-54.176 36.288a32 32 0 1 0 24.512 59.136 228.96 228.96 0 0 0 74.944-50.144zM640 512c0-70.592-57.408-128-128-128s-128 57.408-128 128 57.408 128 128 128 128-57.408 128-128z m-128 64c-35.296 0-64-28.704-64-64s28.704-64 64-64 64 28.704 64 64-28.704 64-64 64z" p-id="56572" fill="#707070"></path></svg>
